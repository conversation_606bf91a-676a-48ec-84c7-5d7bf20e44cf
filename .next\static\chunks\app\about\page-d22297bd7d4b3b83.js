(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[220],{269:(e,a,t)=>{Promise.resolve().then(t.bind(t,5702)),Promise.resolve().then(t.bind(t,3260)),Promise.resolve().then(t.bind(t,7039)),Promise.resolve().then(t.bind(t,7283)),Promise.resolve().then(t.bind(t,449)),Promise.resolve().then(t.bind(t,3317)),Promise.resolve().then(t.bind(t,3714))},449:(e,a,t)=>{"use strict";t.d(a,{ValuesSection:()=>x});var s=t(5155),i=t(2605),r=t(6695),c=t(7580),n=t(9037),l=t(3384),o=t(1976),d=t(1482);let m={"Community First":c.A,"Quality & Authenticity":n.A,Sustainability:l.A,"Warm Hospitality":o.A};function x(){return(0,s.jsx)("section",{className:"py-20 bg-cafe-cream",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6",children:"Our Core Values"}),(0,s.jsx)("p",{className:"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed",children:"These fundamental principles guide every decision we make and every cup we serve, ensuring we stay true to our mission of bringing people together."})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 gap-8 mb-16",children:d.I.values.map((e,a)=>{let t=m[e.title];return(0,s.jsx)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*a},viewport:{once:!0},children:(0,s.jsx)(r.Zp,{className:"h-full border-cafe-beige hover:shadow-cafe-lg transition-all duration-300 group",children:(0,s.jsx)(r.Wu,{className:"p-8",children:(0,s.jsxs)("div",{className:"flex items-start gap-6",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center flex-shrink-0 group-hover:bg-cafe-gold/30 transition-colors duration-300",children:(0,s.jsx)(t,{className:"h-8 w-8 text-cafe-brown"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"font-serif text-2xl font-semibold text-cafe-dark-brown mb-4",children:e.title}),(0,s.jsx)("p",{className:"text-cafe-brown leading-relaxed text-lg",children:e.description})]})]})})})},e.title)})}),(0,s.jsx)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"text-center",children:(0,s.jsx)(r.Zp,{className:"border-cafe-beige bg-white shadow-cafe-lg max-w-4xl mx-auto",children:(0,s.jsxs)(r.Wu,{className:"p-12",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-cafe-gold rounded-full flex items-center justify-center mx-auto mb-6",children:(0,s.jsx)(o.A,{className:"h-8 w-8 text-cafe-dark-brown"})}),(0,s.jsx)("blockquote",{className:"text-2xl md:text-3xl font-serif text-cafe-dark-brown leading-relaxed mb-6 italic",children:"“Our caf\xe9 is more than just a place to grab coffee – it's a community hub where students study, friends catch up, and families create memories.”"}),(0,s.jsxs)("div",{className:"text-cafe-brown",children:[(0,s.jsx)("p",{className:"font-semibold",children:"Founder's Vision"}),(0,s.jsx)("p",{className:"text-sm",children:"Himalayan Brew Caf\xe9"})]})]})})})]})})}},1976:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},3260:(e,a,t)=>{"use strict";t.d(a,{MissionSection:()=>h});var s=t(5155),i=t(2605),r=t(6695),c=t(9946);let n=(0,c.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),l=(0,c.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),o=(0,c.A)("mountain",[["path",{d:"m8 3 4 8 5-5 5 15H2L8 3z",key:"otkl63"}]]);var d=t(3384),m=t(7580);let x=(0,c.A)("compass",[["path",{d:"m16.24 7.76-1.804 5.411a2 2 0 0 1-1.265 1.265L7.76 16.24l1.804-5.411a2 2 0 0 1 1.265-1.265z",key:"9ktpf1"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);function h(){return(0,s.jsx)("section",{className:"py-20 bg-white",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6",children:"Our Mission & Vision"}),(0,s.jsx)("p",{className:"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed",children:"Driven by purpose and guided by vision, we're committed to making a positive impact in Biratnagar and beyond."})]}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-16 items-center mb-20",children:[(0,s.jsxs)(i.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.6},viewport:{once:!0},className:"space-y-8",children:[(0,s.jsx)(r.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300",children:(0,s.jsx)(r.Wu,{className:"p-8",children:(0,s.jsxs)("div",{className:"flex items-start gap-6",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-cafe-brown/10 rounded-full flex items-center justify-center flex-shrink-0",children:(0,s.jsx)(n,{className:"h-8 w-8 text-cafe-brown"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-serif text-2xl font-semibold text-cafe-dark-brown mb-4",children:"Our Mission"}),(0,s.jsx)("p",{className:"text-cafe-brown leading-relaxed text-lg",children:"To create a warm, welcoming space where the community of Biratnagar can come together over exceptional coffee, authentic Nepali cuisine, and meaningful conversations. We strive to support local farmers, preserve traditional flavors, and foster connections that enrich our community."})]})]})})}),(0,s.jsx)(r.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300",children:(0,s.jsx)(r.Wu,{className:"p-8",children:(0,s.jsxs)("div",{className:"flex items-start gap-6",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/10 rounded-full flex items-center justify-center flex-shrink-0",children:(0,s.jsx)(l,{className:"h-8 w-8 text-cafe-brown"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-serif text-2xl font-semibold text-cafe-dark-brown mb-4",children:"Our Vision"}),(0,s.jsx)("p",{className:"text-cafe-brown leading-relaxed text-lg",children:"To become the heart of Biratnagar's social and cultural life, known throughout Nepal as a model of sustainable business practices, community engagement, and authentic hospitality. We envision a future where every cup we serve contributes to a stronger, more connected community."})]})]})})})]}),(0,s.jsxs)(i.P.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"relative",children:[(0,s.jsx)("div",{className:"aspect-[4/3] rounded-2xl overflow-hidden shadow-cafe-lg",children:(0,s.jsx)("img",{src:"https://images.unsplash.com/photo-1521017432531-fbd92d768814?w=600&h=450&fit=crop",alt:"Caf\xe9 community gathering",className:"w-full h-full object-cover"})}),(0,s.jsx)("div",{className:"absolute -bottom-6 -left-6 w-24 h-24 bg-cafe-gold rounded-full flex items-center justify-center shadow-cafe-lg",children:(0,s.jsx)(o,{className:"h-12 w-12 text-cafe-dark-brown"})})]})]}),(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},children:[(0,s.jsx)("h3",{className:"font-serif text-3xl font-semibold text-cafe-dark-brown text-center mb-12",children:"Our Commitment to Biratnagar"}),(0,s.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,s.jsx)(r.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300 text-center",children:(0,s.jsxs)(r.Wu,{className:"p-6",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-cafe-green/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(d.A,{className:"h-8 w-8 text-cafe-green"})}),(0,s.jsx)("h4",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-3",children:"Environmental Responsibility"}),(0,s.jsx)("p",{className:"text-cafe-brown leading-relaxed",children:"Supporting sustainable farming practices and reducing our environmental footprint through eco-friendly operations."})]})}),(0,s.jsx)(r.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300 text-center",children:(0,s.jsxs)(r.Wu,{className:"p-6",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-cafe-brown/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(m.A,{className:"h-8 w-8 text-cafe-brown"})}),(0,s.jsx)("h4",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-3",children:"Community Support"}),(0,s.jsx)("p",{className:"text-cafe-brown leading-relaxed",children:"Actively participating in local events, supporting local artists, and creating opportunities for community engagement."})]})}),(0,s.jsx)(r.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300 text-center",children:(0,s.jsxs)(r.Wu,{className:"p-6",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(x,{className:"h-8 w-8 text-cafe-brown"})}),(0,s.jsx)("h4",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-3",children:"Cultural Preservation"}),(0,s.jsx)("p",{className:"text-cafe-brown leading-relaxed",children:"Celebrating and preserving Nepal's rich coffee culture while embracing modern caf\xe9 experiences."})]})})]})]})]})})}},3384:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},5702:(e,a,t)=>{"use strict";t.d(a,{AboutHero:()=>o});var s=t(5155),i=t(2605),r=t(7312),c=t(7580),n=t(1976),l=t(3384);function o(){return(0,s.jsxs)("section",{className:"relative min-h-[70vh] flex items-center justify-center bg-cafe-pattern pt-16",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-cover bg-center bg-no-repeat",style:{backgroundImage:"url('https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?w=1920&h=1080&fit=crop')",backgroundBlendMode:"overlay"},children:(0,s.jsx)("div",{className:"absolute inset-0 bg-cafe-dark-brown/60"})}),(0,s.jsx)("div",{className:"relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white",children:(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"space-y-8",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(i.P.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.6,delay:.2},className:"flex justify-center",children:(0,s.jsx)(r.A,{className:"h-16 w-16 text-cafe-gold"})}),(0,s.jsx)("h1",{className:"font-serif text-5xl md:text-6xl font-bold",children:"Our Story"}),(0,s.jsx)("p",{className:"text-xl md:text-2xl text-cafe-cream font-medium max-w-3xl mx-auto",children:"Where Mountains Meet Coffee - A Journey of Passion, Community, and Authentic Nepali Hospitality"})]}),(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.6},className:"grid grid-cols-2 md:grid-cols-4 gap-8 mt-16",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(r.A,{className:"h-8 w-8 text-cafe-gold"})}),(0,s.jsx)("div",{className:"text-3xl font-bold text-cafe-gold mb-2",children:"2020"}),(0,s.jsx)("div",{className:"text-cafe-cream",children:"Founded"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(c.A,{className:"h-8 w-8 text-cafe-gold"})}),(0,s.jsx)("div",{className:"text-3xl font-bold text-cafe-gold mb-2",children:"500+"}),(0,s.jsx)("div",{className:"text-cafe-cream",children:"Happy Customers"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(n.A,{className:"h-8 w-8 text-cafe-gold"})}),(0,s.jsx)("div",{className:"text-3xl font-bold text-cafe-gold mb-2",children:"4.9"}),(0,s.jsx)("div",{className:"text-cafe-cream",children:"Rating"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(l.A,{className:"h-8 w-8 text-cafe-gold"})}),(0,s.jsx)("div",{className:"text-3xl font-bold text-cafe-gold mb-2",children:"100%"}),(0,s.jsx)("div",{className:"text-cafe-cream",children:"Local Sourced"})]})]})]})})]})}},6695:(e,a,t)=>{"use strict";t.d(a,{Wu:()=>l,ZB:()=>n,Zp:()=>r,aR:()=>c});var s=t(5155);t(2115);var i=t(9434);function r(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function c(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function n(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",a),...t})}function l(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",a),...t})}},7039:(e,a,t)=>{"use strict";t.d(a,{StorySection:()=>d});var s=t(5155),i=t(2605),r=t(6695),c=t(9074),n=t(7312),l=t(4516),o=t(1482);function d(){let e=o.I.story.split("\n\n");return(0,s.jsx)("section",{className:"py-20 bg-white",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6",children:"The Beginning of Our Journey"}),(0,s.jsx)("p",{className:"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed",children:"Every great story has a beginning. Ours started with a simple dream and a deep love for bringing people together over exceptional coffee."})]}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-16 items-center mb-20",children:[(0,s.jsx)(i.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.6},viewport:{once:!0},className:"space-y-6",children:e.map((e,a)=>(0,s.jsx)(i.P.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*a},viewport:{once:!0},className:"text-cafe-brown leading-relaxed text-lg",children:e},a))}),(0,s.jsxs)(i.P.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"aspect-[4/3] rounded-lg overflow-hidden shadow-cafe",children:(0,s.jsx)("img",{src:"https://images.unsplash.com/photo-1442975631115-c4f7b05b8a2c?w=400&h=300&fit=crop",alt:"Coffee preparation",className:"w-full h-full object-cover"})}),(0,s.jsx)("div",{className:"aspect-square rounded-lg overflow-hidden shadow-cafe",children:(0,s.jsx)("img",{src:"https://images.unsplash.com/photo-1559925393-8be0ec4767c8?w=300&h=300&fit=crop",alt:"Caf\xe9 interior",className:"w-full h-full object-cover"})})]}),(0,s.jsxs)("div",{className:"space-y-4 pt-8",children:[(0,s.jsx)("div",{className:"aspect-square rounded-lg overflow-hidden shadow-cafe",children:(0,s.jsx)("img",{src:"https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=300&fit=crop",alt:"Friends at caf\xe9",className:"w-full h-full object-cover"})}),(0,s.jsx)("div",{className:"aspect-[4/3] rounded-lg overflow-hidden shadow-cafe",children:(0,s.jsx)("img",{src:"https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=400&h=300&fit=crop",alt:"Coffee beans",className:"w-full h-full object-cover"})})]})]})]}),(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"grid md:grid-cols-3 gap-8",children:[(0,s.jsx)(r.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300",children:(0,s.jsxs)(r.Wu,{className:"p-6 text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-cafe-beige rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(c.A,{className:"h-8 w-8 text-cafe-brown"})}),(0,s.jsx)("h3",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-2",children:"2020 - The Dream"}),(0,s.jsx)("p",{className:"text-cafe-brown leading-relaxed",children:"Founded with a vision to create a community space that celebrates Nepal's rich coffee culture and brings people together."})]})}),(0,s.jsx)(r.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300",children:(0,s.jsxs)(r.Wu,{className:"p-6 text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-cafe-beige rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(n.A,{className:"h-8 w-8 text-cafe-brown"})}),(0,s.jsx)("h3",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-2",children:"2021 - Growth"}),(0,s.jsx)("p",{className:"text-cafe-brown leading-relaxed",children:"Established partnerships with local farmers in Ilam and Gulmi, ensuring the finest quality beans and supporting our community."})]})}),(0,s.jsx)(r.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300",children:(0,s.jsxs)(r.Wu,{className:"p-6 text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-cafe-beige rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(l.A,{className:"h-8 w-8 text-cafe-brown"})}),(0,s.jsx)("h3",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-2",children:"Today - Community Hub"}),(0,s.jsx)("p",{className:"text-cafe-brown leading-relaxed",children:"Proud to be Biratnagar's beloved gathering spot, serving hundreds of customers and creating countless memories daily."})]})})]})]})})}},7283:(e,a,t)=>{"use strict";t.d(a,{TeamSection:()=>m});var s=t(5155),i=t(2605),r=t(6695),c=t(7312),n=t(7580),l=t(1976),o=t(9037);let d=[{id:"tm1",name:"Ramesh Sharma",role:"Founder & Head Barista",image:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face",bio:"With over 10 years of experience in the coffee industry, Ramesh founded Himalayan Brew with a vision to bring authentic Nepali coffee culture to Biratnagar.",icon:c.A},{id:"tm2",name:"Sita Gurung",role:"Operations Manager",image:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face",bio:"Sita ensures smooth daily operations and maintains our high standards of service. Her warm personality embodies our commitment to hospitality.",icon:n.A},{id:"tm3",name:"Arjun Rai",role:"Head Chef",image:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face",bio:"Arjun brings traditional Nepali flavors to our menu, specializing in authentic momos and dal bhat that remind customers of home.",icon:l.A},{id:"tm4",name:"Maya Thapa",role:"Community Relations",image:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300&h=300&fit=crop&crop=face",bio:"Maya builds bridges between our caf\xe9 and the Biratnagar community, organizing events and supporting local initiatives.",icon:o.A}];function m(){return(0,s.jsx)("section",{className:"py-20 bg-cafe-beige",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6",children:"Meet Our Team"}),(0,s.jsx)("p",{className:"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed",children:"Behind every great cup of coffee is a passionate team dedicated to creating exceptional experiences for our community."})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16",children:d.map((e,a)=>{let t=e.icon;return(0,s.jsx)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*a},viewport:{once:!0},children:(0,s.jsx)(r.Zp,{className:"h-full border-cafe-sand hover:shadow-cafe-lg transition-all duration-300 group bg-white",children:(0,s.jsxs)(r.Wu,{className:"p-6 text-center",children:[(0,s.jsxs)("div",{className:"relative mb-6",children:[(0,s.jsx)("div",{className:"w-24 h-24 mx-auto rounded-full overflow-hidden shadow-cafe",children:(0,s.jsx)("img",{src:e.image,alt:e.name,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"})}),(0,s.jsx)("div",{className:"absolute -bottom-2 -right-2 w-8 h-8 bg-cafe-gold rounded-full flex items-center justify-center shadow-cafe",children:(0,s.jsx)(t,{className:"h-4 w-4 text-cafe-dark-brown"})})]}),(0,s.jsx)("h3",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-2",children:e.name}),(0,s.jsx)("p",{className:"text-cafe-brown font-medium mb-4",children:e.role}),(0,s.jsx)("p",{className:"text-cafe-brown text-sm leading-relaxed",children:e.bio})]})})},e.id)})}),(0,s.jsx)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"text-center",children:(0,s.jsx)(r.Zp,{className:"border-cafe-sand bg-white shadow-cafe-lg max-w-2xl mx-auto",children:(0,s.jsxs)(r.Wu,{className:"p-8",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-cafe-brown/10 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,s.jsx)(n.A,{className:"h-8 w-8 text-cafe-brown"})}),(0,s.jsx)("h3",{className:"font-serif text-2xl font-semibold text-cafe-dark-brown mb-4",children:"Join Our Family"}),(0,s.jsx)("p",{className:"text-cafe-brown leading-relaxed mb-6",children:"We're always looking for passionate individuals who share our love for coffee and community. If you'd like to be part of the Himalayan Brew family, we'd love to hear from you."}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)("a",{href:"mailto:<EMAIL>",className:"inline-flex items-center justify-center px-6 py-3 bg-cafe-brown text-cafe-cream rounded-lg font-semibold hover:bg-cafe-dark-brown transition-colors duration-200",children:"Send Your Resume"}),(0,s.jsx)("a",{href:"/contact",className:"inline-flex items-center justify-center px-6 py-3 border border-cafe-brown text-cafe-brown rounded-lg font-semibold hover:bg-cafe-brown hover:text-cafe-cream transition-colors duration-200",children:"Contact Us"})]})]})})})]})})}},7580:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9037:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},9074:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])}},e=>{e.O(0,[67,806,441,964,358],()=>e(e.s=269)),_N_E=e.O()}]);