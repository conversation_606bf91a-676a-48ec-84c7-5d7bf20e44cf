"use strict";exports.id=946,exports.ids=[946],exports.modules={6946:(a,b,c)=>{c.d(b,{D:()=>D});var d=c(60687),e=c(43210),f=c(51743),g=c(27605),h=c(63442),i=c(37566),j=c(29523),k=c(44493),l=c(8730),m=c(4780),n=c(78148);function o({className:a,...b}){return(0,d.jsx)(n.b,{"data-slot":"label",className:(0,m.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...b})}let p=g.Op,q=e.createContext({}),r=({...a})=>(0,d.jsx)(q.Provider,{value:{name:a.name},children:(0,d.jsx)(g.xI,{...a})}),s=()=>{let a=e.useContext(q),b=e.useContext(t),{getFieldState:c}=(0,g.xW)(),d=(0,g.lN)({name:a.name}),f=c(a.name,d);if(!a)throw Error("useFormField should be used within <FormField>");let{id:h}=b;return{id:h,name:a.name,formItemId:`${h}-form-item`,formDescriptionId:`${h}-form-item-description`,formMessageId:`${h}-form-item-message`,...f}},t=e.createContext({});function u({className:a,...b}){let c=e.useId();return(0,d.jsx)(t.Provider,{value:{id:c},children:(0,d.jsx)("div",{"data-slot":"form-item",className:(0,m.cn)("grid gap-2",a),...b})})}function v({className:a,...b}){let{error:c,formItemId:e}=s();return(0,d.jsx)(o,{"data-slot":"form-label","data-error":!!c,className:(0,m.cn)("data-[error=true]:text-destructive",a),htmlFor:e,...b})}function w({...a}){let{error:b,formItemId:c,formDescriptionId:e,formMessageId:f}=s();return(0,d.jsx)(l.DX,{"data-slot":"form-control",id:c,"aria-describedby":b?`${e} ${f}`:`${e}`,"aria-invalid":!!b,...a})}function x({className:a,...b}){let{error:c,formMessageId:e}=s(),f=c?String(c?.message??""):b.children;return f?(0,d.jsx)("p",{"data-slot":"form-message",id:e,className:(0,m.cn)("text-destructive text-sm",a),...b,children:f}):null}function y({className:a,type:b,...c}){return(0,d.jsx)("input",{type:b,"data-slot":"input",className:(0,m.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...c})}function z({className:a,...b}){return(0,d.jsx)("textarea",{"data-slot":"textarea",className:(0,m.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...b})}var A=c(5336),B=c(27900);let C=i.Ik({name:i.Yj().min(2,"Name must be at least 2 characters"),email:i.Yj().email("Please enter a valid email address"),phone:i.Yj().optional(),subject:i.Yj().min(5,"Subject must be at least 5 characters"),message:i.Yj().min(10,"Message must be at least 10 characters")});function D(){let[a,b]=(0,e.useState)(!1),[c,i]=(0,e.useState)(!1),l=(0,g.mN)({resolver:(0,h.u)(C),defaultValues:{name:"",email:"",phone:"",subject:"",message:""}}),m=async a=>{i(!0),await new Promise(a=>setTimeout(a,1e3)),console.log("Contact form data:",a),b(!0),i(!1),l.reset()};return a?(0,d.jsxs)(f.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"text-center py-12",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-green rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(A.A,{className:"h-8 w-8 text-white"})}),(0,d.jsx)("h3",{className:"font-serif text-2xl font-semibold text-cafe-dark-brown mb-2",children:"Message Sent Successfully!"}),(0,d.jsx)("p",{className:"text-cafe-brown mb-6",children:"Thank you for reaching out. We'll get back to you within 24 hours."}),(0,d.jsx)(j.$,{onClick:()=>b(!1),variant:"outline",className:"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream",children:"Send Another Message"})]}):(0,d.jsxs)(k.Zp,{className:"border-cafe-beige shadow-cafe-lg",children:[(0,d.jsxs)(k.aR,{children:[(0,d.jsx)(k.ZB,{className:"font-serif text-2xl text-cafe-dark-brown text-center",children:"Get in Touch"}),(0,d.jsx)("p",{className:"text-cafe-brown text-center",children:"Have a question or want to make a reservation? We'd love to hear from you!"})]}),(0,d.jsx)(k.Wu,{children:(0,d.jsx)(p,{...l,children:(0,d.jsxs)("form",{onSubmit:l.handleSubmit(m),className:"space-y-6",children:[(0,d.jsx)(r,{control:l.control,name:"name",render:({field:a})=>(0,d.jsxs)(u,{children:[(0,d.jsx)(v,{className:"text-cafe-dark-brown font-medium",children:"Full Name *"}),(0,d.jsx)(w,{children:(0,d.jsx)(y,{placeholder:"Enter your full name",className:"border-cafe-beige focus:border-cafe-brown",...a})}),(0,d.jsx)(x,{})]})}),(0,d.jsx)(r,{control:l.control,name:"email",render:({field:a})=>(0,d.jsxs)(u,{children:[(0,d.jsx)(v,{className:"text-cafe-dark-brown font-medium",children:"Email Address *"}),(0,d.jsx)(w,{children:(0,d.jsx)(y,{type:"email",placeholder:"Enter your email address",className:"border-cafe-beige focus:border-cafe-brown",...a})}),(0,d.jsx)(x,{})]})}),(0,d.jsx)(r,{control:l.control,name:"phone",render:({field:a})=>(0,d.jsxs)(u,{children:[(0,d.jsx)(v,{className:"text-cafe-dark-brown font-medium",children:"Phone Number (Optional)"}),(0,d.jsx)(w,{children:(0,d.jsx)(y,{type:"tel",placeholder:"Enter your phone number",className:"border-cafe-beige focus:border-cafe-brown",...a})}),(0,d.jsx)(x,{})]})}),(0,d.jsx)(r,{control:l.control,name:"subject",render:({field:a})=>(0,d.jsxs)(u,{children:[(0,d.jsx)(v,{className:"text-cafe-dark-brown font-medium",children:"Subject *"}),(0,d.jsx)(w,{children:(0,d.jsx)(y,{placeholder:"What's this about?",className:"border-cafe-beige focus:border-cafe-brown",...a})}),(0,d.jsx)(x,{})]})}),(0,d.jsx)(r,{control:l.control,name:"message",render:({field:a})=>(0,d.jsxs)(u,{children:[(0,d.jsx)(v,{className:"text-cafe-dark-brown font-medium",children:"Message *"}),(0,d.jsx)(w,{children:(0,d.jsx)(z,{placeholder:"Tell us more about your inquiry...",className:"border-cafe-beige focus:border-cafe-brown min-h-[120px]",...a})}),(0,d.jsx)(x,{})]})}),(0,d.jsx)(j.$,{type:"submit",disabled:c,className:"w-full bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream font-semibold py-3",children:c?(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("div",{className:"w-4 h-4 border-2 border-cafe-cream border-t-transparent rounded-full animate-spin"}),"Sending..."]}):(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(B.A,{className:"h-4 w-4"}),"Send Message"]})})]})})})]})}},44493:(a,b,c)=>{c.d(b,{Wu:()=>i,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}}};