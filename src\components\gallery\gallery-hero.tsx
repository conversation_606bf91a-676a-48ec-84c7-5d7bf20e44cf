"use client";

import { motion } from "framer-motion";
import { Camera, ImageIcon, Users, Coffee } from "lucide-react";

export function GalleryHero() {
  return (
    <section className="relative min-h-[70vh] flex items-center justify-center bg-cafe-pattern pt-16">
      {/* Background Image */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "url('https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=1920&h=1080&fit=crop')",
          backgroundBlendMode: "overlay",
        }}
      >
        <div className="absolute inset-0 bg-cafe-dark-brown/60"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-8"
        >
          {/* Title */}
          <div className="space-y-4">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex justify-center"
            >
              <Camera className="h-16 w-16 text-cafe-gold" />
            </motion.div>
            
            <h1 className="font-serif text-5xl md:text-6xl font-bold">
              Our Gallery
            </h1>
            
            <p className="text-xl md:text-2xl text-cafe-cream font-medium max-w-3xl mx-auto">
              A Visual Journey Through Our Café - Capturing Moments, Memories, and the Magic of Community
            </p>
          </div>

          {/* Gallery Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16"
          >
            <div className="text-center">
              <div className="w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <ImageIcon className="h-8 w-8 text-cafe-gold" />
              </div>
              <div className="text-3xl font-bold text-cafe-gold mb-2">20+</div>
              <div className="text-cafe-cream">Photos</div>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Coffee className="h-8 w-8 text-cafe-gold" />
              </div>
              <div className="text-3xl font-bold text-cafe-gold mb-2">5</div>
              <div className="text-cafe-cream">Categories</div>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-cafe-gold" />
              </div>
              <div className="text-3xl font-bold text-cafe-gold mb-2">100+</div>
              <div className="text-cafe-cream">Happy Moments</div>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Camera className="h-8 w-8 text-cafe-gold" />
              </div>
              <div className="text-3xl font-bold text-cafe-gold mb-2">Daily</div>
              <div className="text-cafe-cream">New Memories</div>
            </div>
          </motion.div>

          {/* Scroll Indicator */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 1 }}
            className="pt-8"
          >
            <p className="text-cafe-cream/80 text-sm">
              Explore our collection of moments and memories
            </p>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
