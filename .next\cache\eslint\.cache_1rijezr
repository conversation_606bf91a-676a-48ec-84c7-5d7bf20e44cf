[{"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\contact\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\gallery\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\layout.tsx": "4", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\menu\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\testimonials\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about\\about-hero.tsx": "8", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about\\mission-section.tsx": "9", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about\\story-section.tsx": "10", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about\\team-section.tsx": "11", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about\\values-section.tsx": "12", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about-section.tsx": "13", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact\\contact-form-section.tsx": "14", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact\\contact-hero.tsx": "15", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact\\contact-info.tsx": "16", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact\\location-map.tsx": "17", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact-form.tsx": "18", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact-section.tsx": "19", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\footer.tsx": "20", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\gallery\\gallery-grid.tsx": "21", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\gallery\\gallery-hero.tsx": "22", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\gallery-preview.tsx": "23", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\hero-section.tsx": "24", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\menu\\menu-categories.tsx": "25", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\menu\\menu-cta.tsx": "26", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\menu\\menu-hero.tsx": "27", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\menu-highlights.tsx": "28", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\navigation.tsx": "29", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\packages-section.tsx": "30", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\testimonials\\testimonials-grid.tsx": "31", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\testimonials\\testimonials-hero.tsx": "32", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\testimonials\\testimonials-stats.tsx": "33", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\testimonials-section.tsx": "34", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\accordion.tsx": "35", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\badge.tsx": "36", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\button.tsx": "37", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\card.tsx": "38", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\carousel.tsx": "39", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\dialog.tsx": "40", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\form.tsx": "41", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\input.tsx": "42", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\label.tsx": "43", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\textarea.tsx": "44", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\cafe-info.ts": "45", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\gallery.ts": "46", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\menu.ts": "47", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\packages.ts": "48", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\testimonials.ts": "49", "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\lib\\utils.ts": "50"}, {"size": 1180, "mtime": 1753252361241, "results": "51", "hashOfConfig": "52"}, {"size": 1099, "mtime": 1753252805353, "results": "53", "hashOfConfig": "52"}, {"size": 881, "mtime": 1753252621778, "results": "54", "hashOfConfig": "52"}, {"size": 1460, "mtime": 1753249865662, "results": "55", "hashOfConfig": "52"}, {"size": 974, "mtime": 1753252513838, "results": "56", "hashOfConfig": "52"}, {"size": 896, "mtime": 1753250144737, "results": "57", "hashOfConfig": "52"}, {"size": 1029, "mtime": 1753252701597, "results": "58", "hashOfConfig": "52"}, {"size": 3701, "mtime": 1753252380862, "results": "59", "hashOfConfig": "52"}, {"size": 7417, "mtime": 1753253322655, "results": "60", "hashOfConfig": "52"}, {"size": 6420, "mtime": 1753252409891, "results": "61", "hashOfConfig": "52"}, {"size": 6402, "mtime": 1753252486696, "results": "62", "hashOfConfig": "52"}, {"size": 4055, "mtime": 1753252427778, "results": "63", "hashOfConfig": "52"}, {"size": 5465, "mtime": 1753252273446, "results": "64", "hashOfConfig": "52"}, {"size": 1291, "mtime": 1753252892798, "results": "65", "hashOfConfig": "52"}, {"size": 4444, "mtime": 1753252828807, "results": "66", "hashOfConfig": "52"}, {"size": 10390, "mtime": 1753253314007, "results": "67", "hashOfConfig": "52"}, {"size": 8871, "mtime": 1753252949604, "results": "68", "hashOfConfig": "52"}, {"size": 7556, "mtime": 1753250279846, "results": "69", "hashOfConfig": "52"}, {"size": 6620, "mtime": 1753250293927, "results": "70", "hashOfConfig": "52"}, {"size": 7145, "mtime": 1753250099674, "results": "71", "hashOfConfig": "52"}, {"size": 10032, "mtime": 1753253497931, "results": "72", "hashOfConfig": "52"}, {"size": 4092, "mtime": 1753252643187, "results": "73", "hashOfConfig": "52"}, {"size": 4817, "mtime": 1753250371697, "results": "74", "hashOfConfig": "52"}, {"size": 4734, "mtime": 1753253009048, "results": "75", "hashOfConfig": "52"}, {"size": 6965, "mtime": 1753252566613, "results": "76", "hashOfConfig": "52"}, {"size": 6893, "mtime": 1753252598463, "results": "77", "hashOfConfig": "52"}, {"size": 4088, "mtime": 1753252535665, "results": "78", "hashOfConfig": "52"}, {"size": 4515, "mtime": 1753249943572, "results": "79", "hashOfConfig": "52"}, {"size": 2875, "mtime": 1753249883045, "results": "80", "hashOfConfig": "52"}, {"size": 5956, "mtime": 1753250307699, "results": "81", "hashOfConfig": "52"}, {"size": 7075, "mtime": 1753252781434, "results": "82", "hashOfConfig": "52"}, {"size": 5145, "mtime": 1753252724837, "results": "83", "hashOfConfig": "52"}, {"size": 7650, "mtime": 1753252753410, "results": "84", "hashOfConfig": "52"}, {"size": 6478, "mtime": 1753250383616, "results": "85", "hashOfConfig": "52"}, {"size": 2053, "mtime": 1753249512148, "results": "86", "hashOfConfig": "52"}, {"size": 1631, "mtime": 1753249984384, "results": "87", "hashOfConfig": "52"}, {"size": 2123, "mtime": 1753249512064, "results": "88", "hashOfConfig": "52"}, {"size": 1989, "mtime": 1753249512030, "results": "89", "hashOfConfig": "52"}, {"size": 5556, "mtime": 1753249512203, "results": "90", "hashOfConfig": "52"}, {"size": 3982, "mtime": 1753249512163, "results": "91", "hashOfConfig": "52"}, {"size": 3759, "mtime": 1753249512128, "results": "92", "hashOfConfig": "52"}, {"size": 967, "mtime": 1753249512138, "results": "93", "hashOfConfig": "52"}, {"size": 611, "mtime": 1753249512134, "results": "94", "hashOfConfig": "52"}, {"size": 759, "mtime": 1753249512141, "results": "95", "hashOfConfig": "52"}, {"size": 2364, "mtime": 1753249731910, "results": "96", "hashOfConfig": "52"}, {"size": 6450, "mtime": 1753252208142, "results": "97", "hashOfConfig": "52"}, {"size": 6643, "mtime": 1753252076870, "results": "98", "hashOfConfig": "52"}, {"size": 4665, "mtime": 1753252247292, "results": "99", "hashOfConfig": "52"}, {"size": 3888, "mtime": 1753252123197, "results": "100", "hashOfConfig": "52"}, {"size": 166, "mtime": 1753249479644, "results": "101", "hashOfConfig": "52"}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1vl3o1q", {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\gallery\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\menu\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\testimonials\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about\\about-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about\\mission-section.tsx", ["252"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about\\story-section.tsx", ["253", "254", "255", "256"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about\\team-section.tsx", ["257"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about\\values-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about-section.tsx", ["258"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact\\contact-form-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact\\contact-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact\\contact-info.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact\\location-map.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\gallery\\gallery-grid.tsx", ["259", "260"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\gallery\\gallery-hero.tsx", ["261"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\gallery-preview.tsx", ["262", "263"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\hero-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\menu\\menu-categories.tsx", ["264"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\menu\\menu-cta.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\menu\\menu-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\menu-highlights.tsx", ["265"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\navigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\packages-section.tsx", ["266"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\testimonials\\testimonials-grid.tsx", ["267"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\testimonials\\testimonials-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\testimonials\\testimonials-stats.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\testimonials-section.tsx", ["268"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\accordion.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\carousel.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\cafe-info.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\gallery.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\menu.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\packages.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\data\\testimonials.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\lib\\utils.ts", [], [], {"ruleId": "269", "severity": 1, "message": "270", "line": 84, "column": 15, "nodeType": "271", "endLine": 88, "endColumn": 17}, {"ruleId": "269", "severity": 1, "message": "270", "line": 63, "column": 17, "nodeType": "271", "endLine": 67, "endColumn": 19}, {"ruleId": "269", "severity": 1, "message": "270", "line": 70, "column": 17, "nodeType": "271", "endLine": 74, "endColumn": 19}, {"ruleId": "269", "severity": 1, "message": "270", "line": 79, "column": 17, "nodeType": "271", "endLine": 83, "endColumn": 19}, {"ruleId": "269", "severity": 1, "message": "270", "line": 86, "column": 17, "nodeType": "271", "endLine": 90, "endColumn": 19}, {"ruleId": "269", "severity": 1, "message": "270", "line": 80, "column": 25, "nodeType": "271", "endLine": 84, "endColumn": 27}, {"ruleId": "269", "severity": 1, "message": "270", "line": 70, "column": 15, "nodeType": "271", "endLine": 74, "endColumn": 17}, {"ruleId": "269", "severity": 1, "message": "270", "line": 127, "column": 23, "nodeType": "271", "endLine": 131, "endColumn": 25}, {"ruleId": "269", "severity": 1, "message": "270", "line": 168, "column": 17, "nodeType": "271", "endLine": 172, "endColumn": 19}, {"ruleId": "272", "severity": 1, "message": "273", "line": 57, "column": 17, "nodeType": "271", "endLine": 57, "endColumn": 61}, {"ruleId": "269", "severity": 1, "message": "270", "line": 48, "column": 21, "nodeType": "271", "endLine": 52, "endColumn": 23}, {"ruleId": "269", "severity": 1, "message": "270", "line": 74, "column": 21, "nodeType": "271", "endLine": 78, "endColumn": 23}, {"ruleId": "269", "severity": 1, "message": "270", "line": 101, "column": 21, "nodeType": "271", "endLine": 105, "endColumn": 23}, {"ruleId": "269", "severity": 1, "message": "270", "line": 47, "column": 19, "nodeType": "271", "endLine": 51, "endColumn": 21}, {"ruleId": "269", "severity": 1, "message": "270", "line": 63, "column": 21, "nodeType": "271", "endLine": 67, "endColumn": 23}, {"ruleId": "269", "severity": 1, "message": "270", "line": 64, "column": 23, "nodeType": "271", "endLine": 68, "endColumn": 25}, {"ruleId": "269", "severity": 1, "message": "270", "line": 79, "column": 27, "nodeType": "271", "endLine": 83, "endColumn": 29}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images."]