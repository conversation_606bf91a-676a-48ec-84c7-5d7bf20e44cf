exports.id=180,exports.ids=[180],exports.modules={4780:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}},13841:()=>{},14246:(a,b,c)=>{"use strict";c.d(b,{Navigation:()=>o});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(29523),i=c(13166),j=c(11860),k=c(12941),l=c(88920),m=c(51743);let n=[{href:"/",label:"Home"},{href:"/about",label:"About"},{href:"/menu",label:"Menu"},{href:"/gallery",label:"Gallery"},{href:"/testimonials",label:"Testimonials"},{href:"/contact",label:"Contact"}];function o(){let[a,b]=(0,e.useState)(!1);return(0,d.jsxs)("nav",{className:"fixed top-0 left-0 right-0 z-50 bg-cafe-cream/95 backdrop-blur-sm border-b border-cafe-beige",children:[(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,d.jsxs)(g(),{href:"/",className:"flex items-center space-x-2",children:[(0,d.jsx)(i.A,{className:"h-8 w-8 text-cafe-brown"}),(0,d.jsx)("span",{className:"font-serif font-bold text-xl text-cafe-dark-brown",children:"Himalayan Brew"})]}),(0,d.jsx)("div",{className:"hidden md:flex items-center space-x-8",children:n.map(a=>(0,d.jsx)(g(),{href:a.href,className:"text-cafe-brown hover:text-cafe-dark-brown transition-colors duration-200 font-medium",children:a.label},a.href))}),(0,d.jsx)("div",{className:"md:hidden",children:(0,d.jsx)(h.$,{variant:"ghost",size:"sm",onClick:()=>b(!a),className:"text-cafe-brown hover:text-cafe-dark-brown",children:a?(0,d.jsx)(j.A,{className:"h-6 w-6"}):(0,d.jsx)(k.A,{className:"h-6 w-6"})})})]})}),(0,d.jsx)(l.N,{children:a&&(0,d.jsx)(m.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"md:hidden bg-cafe-cream border-b border-cafe-beige",children:(0,d.jsx)("div",{className:"px-4 py-4 space-y-4",children:n.map(a=>(0,d.jsx)(g(),{href:a.href,className:"block text-cafe-brown hover:text-cafe-dark-brown transition-colors duration-200 font-medium",onClick:()=>b(!1),children:a.label},a.href))})})})]})}},29523:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(60687);c(43210);var e=c(8730),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},31768:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},40846:(a,b,c)=>{"use strict";c.d(b,{I:()=>d});let d={name:"Himalayan Brew Caf\xe9",tagline:"Where Mountains Meet Coffee",description:"A cozy corner in the heart of Biratnagar, serving authentic Nepali hospitality with the finest coffee and local delicacies.",story:`Nestled in the vibrant city of Biratnagar, Himalayan Brew Caf\xe9 was born from a passion for bringing people together over exceptional coffee and warm conversations. 

Our journey began in 2020 when our founder, inspired by the rich coffee culture of Nepal's hills and the bustling energy of Biratnagar, decided to create a space that celebrates both tradition and modernity.

We source our coffee beans directly from the hills of Ilam and Gulmi, supporting local farmers and ensuring every cup tells a story of Nepal's coffee heritage. Our caf\xe9 is more than just a place to grab coffee – it's a community hub where students study, friends catch up, and families create memories.

Located in the commercial heart of Biratnagar, we've become a beloved gathering spot for locals and visitors alike. Our commitment to quality, sustainability, and community has made us a cornerstone of Biratnagar's growing caf\xe9 culture.`,values:[{title:"Community First",description:"We believe in fostering connections and supporting our local community through every cup we serve."},{title:"Quality & Authenticity",description:"From bean to cup, we maintain the highest standards while honoring traditional Nepali coffee culture."},{title:"Sustainability",description:"We work directly with local farmers and use eco-friendly practices to protect our beautiful Nepal."},{title:"Warm Hospitality",description:"Every guest is treated like family, embodying the true spirit of Nepali hospitality."}],contact:{address:"Main Road, Biratnagar-13, Morang, Nepal",phone:"+977-21-525-789",email:"<EMAIL>",coordinates:{lat:26.4525,lng:87.2718}},hours:{weekdays:"6:00 AM - 10:00 PM",weekends:"7:00 AM - 11:00 PM",holidays:"8:00 AM - 9:00 PM"},socialMedia:{facebook:"https://facebook.com/himalayanbrew",instagram:"https://instagram.com/himalayanbrew",twitter:"https://twitter.com/himalayanbrew"}}},61135:()=>{},64544:(a,b,c)=>{"use strict";c.d(b,{Navigation:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\navigation.tsx","Navigation")},64947:(a,b,c)=>{"use strict";c.d(b,{Footer:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\footer.tsx","Footer")},66616:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},78993:()=>{},94101:(a,b,c)=>{"use strict";c.d(b,{Footer:()=>p});var d=c(60687),e=c(85814),f=c.n(e),g=c(13166),h=c(19526),i=c(66232),j=c(72575),k=c(97992),l=c(48340),m=c(19169),n=c(48730),o=c(40846);function p(){let a=new Date().getFullYear();return(0,d.jsx)("footer",{className:"bg-cafe-dark-brown text-cafe-cream",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,d.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)(f(),{href:"/",className:"flex items-center space-x-2",children:[(0,d.jsx)(g.A,{className:"h-8 w-8 text-cafe-gold"}),(0,d.jsx)("span",{className:"font-serif font-bold text-xl",children:"Himalayan Brew"})]}),(0,d.jsx)("p",{className:"text-cafe-beige leading-relaxed",children:o.I.description}),(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsx)("a",{href:o.I.socialMedia.facebook,target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-cafe-brown rounded-full flex items-center justify-center hover:bg-cafe-gold transition-colors duration-200",children:(0,d.jsx)(h.A,{className:"h-5 w-5"})}),(0,d.jsx)("a",{href:o.I.socialMedia.instagram,target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-cafe-brown rounded-full flex items-center justify-center hover:bg-cafe-gold transition-colors duration-200",children:(0,d.jsx)(i.A,{className:"h-5 w-5"})}),(0,d.jsx)("a",{href:o.I.socialMedia.twitter,target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-cafe-brown rounded-full flex items-center justify-center hover:bg-cafe-gold transition-colors duration-200",children:(0,d.jsx)(j.A,{className:"h-5 w-5"})})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"font-serif text-lg font-semibold text-cafe-gold",children:"Quick Links"}),(0,d.jsxs)("ul",{className:"space-y-2",children:[(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:"/",className:"text-cafe-beige hover:text-cafe-gold transition-colors duration-200",children:"Home"})}),(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:"/about",className:"text-cafe-beige hover:text-cafe-gold transition-colors duration-200",children:"About Us"})}),(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:"/menu",className:"text-cafe-beige hover:text-cafe-gold transition-colors duration-200",children:"Menu"})}),(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:"/gallery",className:"text-cafe-beige hover:text-cafe-gold transition-colors duration-200",children:"Gallery"})}),(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:"/testimonials",className:"text-cafe-beige hover:text-cafe-gold transition-colors duration-200",children:"Testimonials"})}),(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:"/contact",className:"text-cafe-beige hover:text-cafe-gold transition-colors duration-200",children:"Contact"})})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"font-serif text-lg font-semibold text-cafe-gold",children:"Contact Info"}),(0,d.jsxs)("ul",{className:"space-y-3",children:[(0,d.jsxs)("li",{className:"flex items-start gap-3",children:[(0,d.jsx)(k.A,{className:"h-5 w-5 text-cafe-gold mt-0.5 flex-shrink-0"}),(0,d.jsx)("span",{className:"text-cafe-beige",children:o.I.contact.address})]}),(0,d.jsxs)("li",{className:"flex items-center gap-3",children:[(0,d.jsx)(l.A,{className:"h-5 w-5 text-cafe-gold flex-shrink-0"}),(0,d.jsx)("a",{href:`tel:${o.I.contact.phone}`,className:"text-cafe-beige hover:text-cafe-gold transition-colors duration-200",children:o.I.contact.phone})]}),(0,d.jsxs)("li",{className:"flex items-center gap-3",children:[(0,d.jsx)(m.A,{className:"h-5 w-5 text-cafe-gold flex-shrink-0"}),(0,d.jsx)("a",{href:`mailto:${o.I.contact.email}`,className:"text-cafe-beige hover:text-cafe-gold transition-colors duration-200",children:o.I.contact.email})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"font-serif text-lg font-semibold text-cafe-gold",children:"Opening Hours"}),(0,d.jsxs)("ul",{className:"space-y-2",children:[(0,d.jsxs)("li",{className:"flex items-start gap-3",children:[(0,d.jsx)(n.A,{className:"h-5 w-5 text-cafe-gold mt-0.5 flex-shrink-0"}),(0,d.jsxs)("div",{className:"text-cafe-beige",children:[(0,d.jsx)("div",{className:"font-medium",children:"Monday - Friday"}),(0,d.jsx)("div",{className:"text-sm",children:o.I.hours.weekdays})]})]}),(0,d.jsxs)("li",{className:"flex items-start gap-3",children:[(0,d.jsx)(n.A,{className:"h-5 w-5 text-cafe-gold mt-0.5 flex-shrink-0"}),(0,d.jsxs)("div",{className:"text-cafe-beige",children:[(0,d.jsx)("div",{className:"font-medium",children:"Saturday - Sunday"}),(0,d.jsx)("div",{className:"text-sm",children:o.I.hours.weekends})]})]}),(0,d.jsxs)("li",{className:"flex items-start gap-3",children:[(0,d.jsx)(n.A,{className:"h-5 w-5 text-cafe-gold mt-0.5 flex-shrink-0"}),(0,d.jsxs)("div",{className:"text-cafe-beige",children:[(0,d.jsx)("div",{className:"font-medium",children:"Public Holidays"}),(0,d.jsx)("div",{className:"text-sm",children:o.I.hours.holidays})]})]})]})]})]}),(0,d.jsxs)("div",{className:"border-t border-cafe-brown mt-12 pt-8 flex flex-col md:flex-row justify-between items-center",children:[(0,d.jsxs)("p",{className:"text-cafe-beige text-sm",children:["\xa9 ",a," Himalayan Brew Caf\xe9. All rights reserved."]}),(0,d.jsxs)("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[(0,d.jsx)(f(),{href:"/privacy",className:"text-cafe-beige hover:text-cafe-gold text-sm transition-colors duration-200",children:"Privacy Policy"}),(0,d.jsx)(f(),{href:"/terms",className:"text-cafe-beige hover:text-cafe-gold text-sm transition-colors duration-200",children:"Terms of Service"})]})]})]})})}},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(37413),e=c(25091),f=c.n(e),g=c(31386),h=c.n(g);c(61135);let i={title:"Himalayan Brew Caf\xe9 - Best Caf\xe9 in Biratnagar | Cozy Coffee Shop Nepal",description:"Experience the finest coffee and authentic Nepali hospitality at Himalayan Brew Caf\xe9 in Biratnagar. Serving premium coffee, traditional momos, and creating memorable moments since 2020.",keywords:"caf\xe9 in Biratnagar, best caf\xe9 Biratnagar, cozy coffee shop Biratnagar, Himalayan Brew, Nepal coffee, momos Biratnagar, coffee shop Nepal",authors:[{name:"Himalayan Brew Caf\xe9"}],openGraph:{title:"Himalayan Brew Caf\xe9 - Where Mountains Meet Coffee",description:"A cozy corner in the heart of Biratnagar, serving authentic Nepali hospitality with the finest coffee and local delicacies.",url:"https://himalayanbrew.com.np",siteName:"Himalayan Brew Caf\xe9",locale:"en_US",type:"website"}};function j({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} font-sans antialiased`,children:a})})}}};