"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { MapPin, Calendar, Coffee } from "lucide-react";
import Image from "next/image";
import { cafeInfo } from "@/data/cafe-info";

export function StorySection() {
  const storyParagraphs = cafeInfo.story.split('\n\n');

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6">
            The Beginning of Our Journey
          </h2>
          <p className="text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed">
            Every great story has a beginning. Ours started with a simple dream and a deep love for bringing people together over exceptional coffee.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Story Content */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            {storyParagraphs.map((paragraph, index) => (
              <motion.p
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-cafe-brown leading-relaxed text-lg"
              >
                {paragraph}
              </motion.p>
            ))}
          </motion.div>

          {/* Image Grid */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="grid grid-cols-2 gap-4"
          >
            <div className="space-y-4">
              <div className="aspect-[4/3] rounded-lg overflow-hidden shadow-cafe">
                <Image
                  src="https://images.unsplash.com/photo-1442975631115-c4f7b05b8a2c?w=400&h=300&fit=crop"
                  alt="Coffee preparation"
                  width={400}
                  height={300}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden shadow-cafe">
                <Image
                  src="https://images.unsplash.com/photo-1559925393-8be0ec4767c8?w=300&h=300&fit=crop"
                  alt="Café interior"
                  width={300}
                  height={300}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
            <div className="space-y-4 pt-8">
              <div className="aspect-square rounded-lg overflow-hidden shadow-cafe">
                <Image
                  src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=300&fit=crop"
                  alt="Friends at café"
                  width={300}
                  height={300}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-[4/3] rounded-lg overflow-hidden shadow-cafe">
                <Image
                  src="https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=400&h=300&fit=crop"
                  alt="Coffee beans"
                  width={400}
                  height={300}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </motion.div>
        </div>

        {/* Timeline Cards */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="grid md:grid-cols-3 gap-8"
        >
          <Card className="border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300">
            <CardContent className="p-6 text-center">
              <div className="w-16 h-16 bg-cafe-beige rounded-full flex items-center justify-center mx-auto mb-4">
                <Calendar className="h-8 w-8 text-cafe-brown" />
              </div>
              <h3 className="font-serif text-xl font-semibold text-cafe-dark-brown mb-2">
                2020 - The Dream
              </h3>
              <p className="text-cafe-brown leading-relaxed">
                Founded with a vision to create a community space that celebrates Nepal&apos;s rich coffee culture and brings people together.
              </p>
            </CardContent>
          </Card>

          <Card className="border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300">
            <CardContent className="p-6 text-center">
              <div className="w-16 h-16 bg-cafe-beige rounded-full flex items-center justify-center mx-auto mb-4">
                <Coffee className="h-8 w-8 text-cafe-brown" />
              </div>
              <h3 className="font-serif text-xl font-semibold text-cafe-dark-brown mb-2">
                2021 - Growth
              </h3>
              <p className="text-cafe-brown leading-relaxed">
                Established partnerships with local farmers in Ilam and Gulmi, ensuring the finest quality beans and supporting our community.
              </p>
            </CardContent>
          </Card>

          <Card className="border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300">
            <CardContent className="p-6 text-center">
              <div className="w-16 h-16 bg-cafe-beige rounded-full flex items-center justify-center mx-auto mb-4">
                <MapPin className="h-8 w-8 text-cafe-brown" />
              </div>
              <h3 className="font-serif text-xl font-semibold text-cafe-dark-brown mb-2">
                Today - Community Hub
              </h3>
              <p className="text-cafe-brown leading-relaxed">
                Proud to be Biratnagar&apos;s beloved gathering spot, serving hundreds of customers and creating countless memories daily.
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
}
