@import "tailwindcss";

@theme {
  /* Vibrant Café Color Palette */
  --color-cafe-cream: #FFF8F0;
  --color-cafe-beige: #F4E4C1;
  --color-cafe-sand: #E6C79C;
  --color-cafe-brown: #A0522D;
  --color-cafe-dark-brown: #6B4423;
  --color-cafe-green: #228B22;
  --color-cafe-dark-green: #006400;
  --color-cafe-gold: #FFD700;
  --color-cafe-orange: #FF8C00;
  --color-cafe-red: #DC143C;
  --color-cafe-espresso: #3C2415;

  --radius: 0.5rem;

  /* ShadCN color palette - Updated with vibrant colors */
  --color-background: #FFF8F0;
  --color-foreground: #3C2415;
  --color-card: #FFFFFF;
  --color-card-foreground: #3C2415;
  --color-popover: #FFFFFF;
  --color-popover-foreground: #3C2415;
  --color-primary: #A0522D;
  --color-primary-foreground: #FFF8F0;
  --color-secondary: #F4E4C1;
  --color-secondary-foreground: #3C2415;
  --color-muted: #E6C79C;
  --color-muted-foreground: #6B4423;
  --color-accent: #FFD700;
  --color-accent-foreground: #3C2415;
  --color-destructive: #DC143C;
  --color-destructive-foreground: #FFF8F0;
  --color-border: #E6C79C;
  --color-input: #F4E4C1;
  --color-ring: #A0522D;
}

body {
  background-color: var(--color-cafe-cream);
  color: var(--color-cafe-dark-brown);
  font-family: var(--font-inter), system-ui, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-playfair), Georgia, serif;
  color: var(--color-cafe-dark-brown);
}

h1 {
  font-size: 2.25rem;
  font-weight: 700;
  line-height: 1.2;
}

@media (min-width: 768px) {
  h1 {
    font-size: 3rem;
  }
}

@media (min-width: 1024px) {
  h1 {
    font-size: 3.75rem;
  }
}

h2 {
  font-size: 1.875rem;
  font-weight: 600;
  line-height: 1.3;
}

@media (min-width: 768px) {
  h2 {
    font-size: 2.25rem;
  }
}

h3 {
  font-size: 1.5rem;
  font-weight: 500;
  line-height: 1.4;
}

@media (min-width: 768px) {
  h3 {
    font-size: 1.875rem;
  }
}

p {
  color: var(--color-cafe-brown);
  line-height: 1.625;
}

.text-gradient {
  background: linear-gradient(135deg, var(--color-cafe-brown), var(--color-cafe-orange), var(--color-cafe-gold));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.text-gradient-green {
  background: linear-gradient(135deg, var(--color-cafe-green), var(--color-cafe-dark-green));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.bg-cafe-pattern {
  background-image:
    radial-gradient(circle at 25% 25%, rgba(160, 82, 45, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(34, 139, 34, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(255, 140, 0, 0.05) 0%, transparent 70%);
}

.shadow-cafe {
  box-shadow: 0 4px 6px -1px rgba(160, 82, 45, 0.15), 0 2px 4px -1px rgba(160, 82, 45, 0.08);
}

.shadow-cafe-lg {
  box-shadow: 0 10px 15px -3px rgba(160, 82, 45, 0.15), 0 4px 6px -2px rgba(160, 82, 45, 0.08);
}

.shadow-cafe-orange {
  box-shadow: 0 4px 6px -1px rgba(255, 140, 0, 0.2), 0 2px 4px -1px rgba(255, 140, 0, 0.1);
}

.shadow-cafe-green {
  box-shadow: 0 4px 6px -1px rgba(34, 139, 34, 0.2), 0 2px 4px -1px rgba(34, 139, 34, 0.1);
}
