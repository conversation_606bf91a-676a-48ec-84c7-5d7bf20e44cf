"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Camera, Filter, X, ChevronLeft, ChevronRight } from "lucide-react";
import { galleryImages, galleryCategories, getImagesByCategory, GalleryImage } from "@/data/gallery";
import Image from "next/image";

export function GalleryGrid() {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [showFilters, setShowFilters] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);

  const filteredImages = selectedCategory === "all"
    ? galleryImages
    : getImagesByCategory(selectedCategory as GalleryImage['category']);

  const openModal = (index: number) => {
    setSelectedImageIndex(index);
  };

  const closeModal = () => {
    setSelectedImageIndex(null);
  };

  const navigateImage = (direction: 'prev' | 'next') => {
    if (selectedImageIndex === null) return;
    
    const newIndex = direction === 'prev' 
      ? (selectedImageIndex - 1 + filteredImages.length) % filteredImages.length
      : (selectedImageIndex + 1) % filteredImages.length;
    
    setSelectedImageIndex(newIndex);
  };

  const selectedImage = selectedImageIndex !== null ? filteredImages[selectedImageIndex] : null;

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6">
            Moments & Memories
          </h2>
          <p className="text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed">
            Every photo tells a story of connection, flavor, and the warm hospitality that makes Himalayan Brew special.
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="mb-12"
        >
          {/* Mobile Filter Toggle */}
          <div className="md:hidden mb-4">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="w-full border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filter by Category
            </Button>
          </div>

          {/* Category Buttons */}
          <div className={`${showFilters ? 'block' : 'hidden'} md:block`}>
            <div className="flex flex-wrap justify-center gap-3">
              {galleryCategories.map((category) => (
                <Button
                  key={category.key}
                  variant={selectedCategory === category.key ? "default" : "outline"}
                  onClick={() => setSelectedCategory(category.key)}
                  className={`${
                    selectedCategory === category.key
                      ? "bg-cafe-brown text-cafe-cream hover:bg-cafe-dark-brown"
                      : "border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream"
                  } transition-colors duration-200`}
                >
                  {category.label}
                </Button>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Gallery Grid */}
        <AnimatePresence mode="wait">
          <motion.div
            key={selectedCategory}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.4 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          >
            {filteredImages.map((image, index) => (
              <motion.div
                key={image.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.05 }}
                className={`relative group cursor-pointer overflow-hidden rounded-lg ${
                  index % 7 === 0 ? 'md:col-span-2 md:row-span-2' : 
                  index % 5 === 0 ? 'lg:col-span-2' : ''
                }`}
              >
                <Dialog>
                  <DialogTrigger asChild>
                    <div 
                      className="relative aspect-square overflow-hidden rounded-lg"
                      onClick={() => openModal(index)}
                    >
                      <Image
                        src={image.src}
                        alt={image.alt}
                        width={400}
                        height={400}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                      
                      {/* Overlay */}
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-colors duration-300 flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                            <Camera className="h-6 w-6 text-white" />
                          </div>
                        </div>
                      </div>

                      {/* Category Badge */}
                      <div className="absolute top-3 left-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <Badge className="bg-cafe-brown/80 text-cafe-cream text-xs backdrop-blur-sm">
                          {image.category}
                        </Badge>
                      </div>

                      {/* Title */}
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <h3 className="text-white font-semibold text-sm">
                          {image.title}
                        </h3>
                      </div>
                    </div>
                  </DialogTrigger>
                </Dialog>
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>

        {/* Image Modal */}
        {selectedImage && (
          <Dialog open={selectedImageIndex !== null} onOpenChange={closeModal}>
            <DialogContent className="max-w-6xl w-full p-0 border-0 bg-transparent">
              <div className="relative">
                <Image
                  src={selectedImage.src}
                  alt={selectedImage.alt}
                  width={800}
                  height={600}
                  className="w-full h-auto max-h-[80vh] object-contain rounded-lg"
                />
                
                {/* Navigation Buttons */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigateImage('prev')}
                  className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/20 hover:bg-black/40 text-white border-0"
                >
                  <ChevronLeft className="h-6 w-6" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigateImage('next')}
                  className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/20 hover:bg-black/40 text-white border-0"
                >
                  <ChevronRight className="h-6 w-6" />
                </Button>

                {/* Close Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={closeModal}
                  className="absolute top-4 right-4 bg-black/20 hover:bg-black/40 text-white border-0"
                >
                  <X className="h-6 w-6" />
                </Button>

                {/* Image Info */}
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6 rounded-b-lg">
                  <h3 className="text-white font-serif text-xl font-semibold mb-2">
                    {selectedImage.title}
                  </h3>
                  {selectedImage.description && (
                    <p className="text-white/90 text-sm">
                      {selectedImage.description}
                    </p>
                  )}
                  <div className="flex items-center gap-2 mt-3">
                    <Badge className="bg-cafe-brown text-cafe-cream">
                      {selectedImage.category}
                    </Badge>
                    <span className="text-white/70 text-sm">
                      {selectedImageIndex! + 1} of {filteredImages.length}
                    </span>
                  </div>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}

        {/* Results Count */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.2 }}
          className="text-center mt-12"
        >
          <p className="text-cafe-brown">
            Showing {filteredImages.length} {filteredImages.length === 1 ? 'photo' : 'photos'}
            {selectedCategory !== "all" && ` in ${galleryCategories.find(c => c.key === selectedCategory)?.label}`}
          </p>
        </motion.div>
      </div>
    </section>
  );
}
