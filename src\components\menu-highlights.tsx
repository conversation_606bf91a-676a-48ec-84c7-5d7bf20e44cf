"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Star, Leaf } from "lucide-react";
import { popularItems } from "@/data/menu";
import Link from "next/link";
import Image from "next/image";

export function MenuHighlights() {
  // Get first 6 popular items for highlights
  const highlightItems = popularItems.slice(0, 6);

  return (
    <section className="py-20 bg-cafe-cream">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6">
            <PERSON>u <PERSON>
          </h2>
          <p className="text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed">
            Discover our most beloved dishes and drinks, crafted with love and the finest ingredients from Nepal and beyond.
          </p>
        </motion.div>

        {/* Menu Items Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {highlightItems.map((item, index) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full overflow-hidden border-cafe-beige hover:shadow-cafe-lg transition-all duration-300 group">
                {/* Image */}
                <div className="relative aspect-[4/3] overflow-hidden">
                  <img
                    src={item.image}
                    alt={item.name}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4 flex gap-2">
                    {item.isPopular && (
                      <Badge className="bg-cafe-gold text-cafe-dark-brown font-semibold">
                        <Star className="h-3 w-3 mr-1" />
                        Popular
                      </Badge>
                    )}
                    {item.isVegetarian && (
                      <Badge variant="secondary" className="bg-cafe-green text-white">
                        <Leaf className="h-3 w-3 mr-1" />
                        Veg
                      </Badge>
                    )}
                  </div>
                </div>

                <CardContent className="p-6">
                  <div className="space-y-3">
                    {/* Name and Price */}
                    <div className="flex justify-between items-start">
                      <h3 className="font-serif text-xl font-semibold text-cafe-dark-brown">
                        {item.name}
                      </h3>
                      <span className="text-lg font-bold text-cafe-brown">
                        Rs. {item.price}
                      </span>
                    </div>

                    {/* Description */}
                    <p className="text-cafe-brown leading-relaxed text-sm">
                      {item.description}
                    </p>

                    {/* Category */}
                    <div className="pt-2">
                      <Badge variant="outline" className="border-cafe-beige text-cafe-brown">
                        {item.category}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* CTA Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <Link href="/menu">
            <Button 
              size="lg" 
              className="bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream px-8 py-3 text-lg font-semibold shadow-cafe-lg"
            >
              View Full Menu
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  );
}
