(()=>{var a={};a.id=977,a.ids=[977],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8859:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,43839)),"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\contact\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\contact\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/contact/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11938:(a,b,c)=>{"use strict";c.d(b,{LocationMap:()=>q});var d=c(60687),e=c(51743),f=c(44493),g=c(29523),h=c(97992),i=c(92200),j=c(62688);let k=(0,j.A)("car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]]),l=(0,j.A)("bus",[["path",{d:"M8 6v6",key:"18i7km"}],["path",{d:"M15 6v6",key:"1sg6z9"}],["path",{d:"M2 12h19.6",key:"de5uta"}],["path",{d:"M18 18h3s.5-1.7.8-2.8c.1-.4.2-.8.2-1.2 0-.4-.1-.8-.2-1.2l-1.4-5C20.1 6.8 19.1 6 18 6H4a2 2 0 0 0-2 2v10h3",key:"1wwztk"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}],["path",{d:"M9 18h5",key:"lrx6i"}],["circle",{cx:"16",cy:"18",r:"2",key:"1v4tcr"}]]),m=(0,j.A)("bike",[["circle",{cx:"18.5",cy:"17.5",r:"3.5",key:"15x4ox"}],["circle",{cx:"5.5",cy:"17.5",r:"3.5",key:"1noe27"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["path",{d:"M12 17.5V14l-3-3 4-3 2 3h2",key:"1npguv"}]]);var n=c(48340),o=c(19169),p=c(40846);function q(){return(0,d.jsx)("section",{className:"py-20 bg-white",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6",children:"Find Your Way to Us"}),(0,d.jsx)("p",{className:"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed",children:"We're conveniently located on Main Road in Biratnagar, making us easily accessible by any mode of transportation."})]}),(0,d.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12",children:[(0,d.jsx)(e.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.6},viewport:{once:!0},children:(0,d.jsx)(f.Zp,{className:"border-cafe-beige shadow-cafe-lg overflow-hidden",children:(0,d.jsx)(f.Wu,{className:"p-0",children:(0,d.jsxs)("div",{className:"aspect-[4/3] bg-cafe-beige relative",children:[(0,d.jsx)("div",{className:"w-full h-full flex items-center justify-center bg-gradient-to-br from-cafe-beige to-cafe-sand",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)(h.A,{className:"h-16 w-16 mx-auto mb-4 text-cafe-brown"}),(0,d.jsx)("h3",{className:"font-serif text-2xl font-semibold text-cafe-dark-brown mb-2",children:"Interactive Map"}),(0,d.jsx)("p",{className:"text-cafe-brown mb-4",children:p.I.contact.address}),(0,d.jsxs)(g.$,{className:"bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream",children:[(0,d.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Open in Maps"]})]})}),(0,d.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",children:(0,d.jsx)("div",{className:"w-8 h-8 bg-cafe-gold rounded-full flex items-center justify-center shadow-cafe-lg animate-pulse",children:(0,d.jsx)(h.A,{className:"h-5 w-5 text-cafe-dark-brown"})})})]})})})}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"space-y-6",children:[(0,d.jsx)("h3",{className:"font-serif text-2xl font-semibold text-cafe-dark-brown mb-6",children:"How to Get Here"}),(0,d.jsx)(f.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300",children:(0,d.jsx)(f.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-start gap-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0",children:(0,d.jsx)(k,{className:"h-6 w-6 text-cafe-brown"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold text-cafe-dark-brown mb-2",children:"By Car"}),(0,d.jsx)("p",{className:"text-cafe-brown text-sm leading-relaxed",children:"Located on Main Road, easily accessible from all parts of Biratnagar. Parking available nearby. Look for our distinctive signage."})]})]})})}),(0,d.jsx)(f.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300",children:(0,d.jsx)(f.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-start gap-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0",children:(0,d.jsx)(l,{className:"h-6 w-6 text-cafe-brown"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold text-cafe-dark-brown mb-2",children:"By Public Transport"}),(0,d.jsx)("p",{className:"text-cafe-brown text-sm leading-relaxed",children:"Multiple bus routes stop near our location. Get off at Main Road bus stop and walk 2 minutes. Rickshaws and taxis are readily available."})]})]})})}),(0,d.jsx)(f.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300",children:(0,d.jsx)(f.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-start gap-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0",children:(0,d.jsx)(m,{className:"h-6 w-6 text-cafe-brown"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold text-cafe-dark-brown mb-2",children:"By Bike or Walking"}),(0,d.jsx)("p",{className:"text-cafe-brown text-sm leading-relaxed",children:"Bike parking available. We're in a pedestrian-friendly area with wide sidewalks. Perfect for a morning walk to grab your coffee!"})]})]})})}),(0,d.jsx)(f.Zp,{className:"border-cafe-beige bg-cafe-cream",children:(0,d.jsxs)(f.Wu,{className:"p-6",children:[(0,d.jsx)("h4",{className:"font-semibold text-cafe-dark-brown mb-3",children:"Nearby Landmarks"}),(0,d.jsxs)("ul",{className:"text-cafe-brown text-sm space-y-2",children:[(0,d.jsx)("li",{children:"• Next to City Bank (2 minutes walk)"}),(0,d.jsx)("li",{children:"• Opposite Biratnagar Shopping Complex"}),(0,d.jsx)("li",{children:"• Near Traffic Police Office"}),(0,d.jsx)("li",{children:"• 5 minutes from Biratnagar Airport"})]})]})}),(0,d.jsx)("div",{className:"pt-4",children:(0,d.jsxs)(g.$,{size:"lg",className:"w-full bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream font-semibold",children:[(0,d.jsx)(i.A,{className:"h-5 w-5 mr-2"}),"Get Directions"]})})]})]}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"text-center mt-16",children:(0,d.jsx)(f.Zp,{className:"border-cafe-beige bg-cafe-cream max-w-2xl mx-auto",children:(0,d.jsxs)(f.Wu,{className:"p-8",children:[(0,d.jsx)("h3",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-4",children:"Need Help Finding Us?"}),(0,d.jsx)("p",{className:"text-cafe-brown leading-relaxed mb-6",children:"If you're having trouble finding our location, don't hesitate to call us. Our friendly staff will be happy to guide you to our caf\xe9."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)("a",{href:`tel:${p.I.contact.phone}`,children:(0,d.jsxs)(g.$,{className:"bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream",children:[(0,d.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"Call for Directions"]})}),(0,d.jsx)("a",{href:`mailto:${p.I.contact.email}`,children:(0,d.jsxs)(g.$,{variant:"outline",className:"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream",children:[(0,d.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Email Us"]})})]})]})})})]})})}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30262:(a,b,c)=>{"use strict";c.d(b,{ContactInfo:()=>q});var d=c(60687),e=c(51743),f=c(44493),g=c(29523),h=c(97992),i=c(92200),j=c(48340),k=c(19169),l=c(48730),m=c(19526),n=c(66232),o=c(72575),p=c(40846);function q(){return(0,d.jsx)("section",{className:"py-20 bg-white",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6",children:"How to Find Us"}),(0,d.jsx)("p",{className:"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed",children:"Located in the bustling heart of Biratnagar, we're easy to find and always ready to welcome you with a warm smile and exceptional coffee."})]}),(0,d.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 mb-16",children:[(0,d.jsxs)(e.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.6},viewport:{once:!0},className:"space-y-8",children:[(0,d.jsx)(f.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300",children:(0,d.jsx)(f.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-start gap-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0",children:(0,d.jsx)(h.A,{className:"h-6 w-6 text-cafe-brown"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-2",children:"Our Location"}),(0,d.jsx)("p",{className:"text-cafe-brown leading-relaxed mb-4",children:p.I.contact.address}),(0,d.jsxs)(g.$,{variant:"outline",size:"sm",className:"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream",children:[(0,d.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Get Directions"]})]})]})})}),(0,d.jsx)(f.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300",children:(0,d.jsx)(f.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-start gap-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0",children:(0,d.jsx)(j.A,{className:"h-6 w-6 text-cafe-brown"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-2",children:"Call Us"}),(0,d.jsx)("p",{className:"text-cafe-brown leading-relaxed mb-4",children:"Have questions or want to make a reservation? Give us a call!"}),(0,d.jsx)("a",{href:`tel:${p.I.contact.phone}`,children:(0,d.jsxs)(g.$,{variant:"outline",size:"sm",className:"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream",children:[(0,d.jsx)(j.A,{className:"h-4 w-4 mr-2"}),p.I.contact.phone]})})]})]})})}),(0,d.jsx)(f.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300",children:(0,d.jsx)(f.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-start gap-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0",children:(0,d.jsx)(k.A,{className:"h-6 w-6 text-cafe-brown"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-2",children:"Email Us"}),(0,d.jsx)("p",{className:"text-cafe-brown leading-relaxed mb-4",children:"Send us your feedback, suggestions, or business inquiries."}),(0,d.jsx)("a",{href:`mailto:${p.I.contact.email}`,children:(0,d.jsxs)(g.$,{variant:"outline",size:"sm",className:"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream",children:[(0,d.jsx)(k.A,{className:"h-4 w-4 mr-2"}),p.I.contact.email]})})]})]})})})]}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"space-y-8",children:[(0,d.jsx)(f.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300",children:(0,d.jsx)(f.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-start gap-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0",children:(0,d.jsx)(l.A,{className:"h-6 w-6 text-cafe-brown"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-4",children:"Opening Hours"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-cafe-brown font-medium",children:"Monday - Friday"}),(0,d.jsx)("span",{className:"text-cafe-dark-brown font-semibold",children:p.I.hours.weekdays})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-cafe-brown font-medium",children:"Saturday - Sunday"}),(0,d.jsx)("span",{className:"text-cafe-dark-brown font-semibold",children:p.I.hours.weekends})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-cafe-brown font-medium",children:"Public Holidays"}),(0,d.jsx)("span",{className:"text-cafe-dark-brown font-semibold",children:p.I.hours.holidays})]})]})]})]})})}),(0,d.jsx)(f.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300",children:(0,d.jsxs)(f.Wu,{className:"p-6",children:[(0,d.jsx)("h3",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-4",children:"Follow Us"}),(0,d.jsx)("p",{className:"text-cafe-brown leading-relaxed mb-6",children:"Stay connected with us on social media for updates, special offers, and behind-the-scenes moments."}),(0,d.jsxs)("div",{className:"flex gap-4",children:[(0,d.jsx)("a",{href:p.I.socialMedia.facebook,target:"_blank",rel:"noopener noreferrer",className:"w-12 h-12 bg-cafe-brown rounded-full flex items-center justify-center hover:bg-cafe-dark-brown transition-colors duration-200 text-cafe-cream",children:(0,d.jsx)(m.A,{className:"h-5 w-5"})}),(0,d.jsx)("a",{href:p.I.socialMedia.instagram,target:"_blank",rel:"noopener noreferrer",className:"w-12 h-12 bg-cafe-brown rounded-full flex items-center justify-center hover:bg-cafe-dark-brown transition-colors duration-200 text-cafe-cream",children:(0,d.jsx)(n.A,{className:"h-5 w-5"})}),(0,d.jsx)("a",{href:p.I.socialMedia.twitter,target:"_blank",rel:"noopener noreferrer",className:"w-12 h-12 bg-cafe-brown rounded-full flex items-center justify-center hover:bg-cafe-dark-brown transition-colors duration-200 text-cafe-cream",children:(0,d.jsx)(o.A,{className:"h-5 w-5"})})]})]})}),(0,d.jsx)(f.Zp,{className:"border-cafe-beige bg-cafe-cream",children:(0,d.jsxs)(f.Wu,{className:"p-6 text-center",children:[(0,d.jsx)("h3",{className:"font-serif text-lg font-semibold text-cafe-dark-brown mb-3",children:"Special Events & Private Bookings"}),(0,d.jsx)("p",{className:"text-cafe-brown text-sm leading-relaxed mb-4",children:"Planning a special event? We offer private bookings for birthdays, meetings, and celebrations. Contact us to discuss your requirements."}),(0,d.jsx)(g.$,{size:"sm",className:"bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream",children:"Inquire About Events"})]})})]})]})]})})}},30906:(a,b,c)=>{Promise.resolve().then(c.bind(c,65891)),Promise.resolve().then(c.bind(c,81128)),Promise.resolve().then(c.bind(c,65412)),Promise.resolve().then(c.bind(c,96713)),Promise.resolve().then(c.bind(c,64947)),Promise.resolve().then(c.bind(c,64544))},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},43839:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>l,metadata:()=>k});var d=c(37413),e=c(64544),f=c(64947),g=c(81128),h=c(65412),i=c(65891),j=c(96713);let k={title:"Contact Us - Himalayan Brew Caf\xe9 | Visit Us in Biratnagar",description:"Get in touch with Himalayan Brew Caf\xe9 in Biratnagar. Find our location, hours, contact information, and send us a message. We'd love to hear from you!",keywords:"contact Himalayan Brew, caf\xe9 location Biratnagar, coffee shop address Nepal, visit caf\xe9 Biratnagar, contact information"};function l(){return(0,d.jsxs)("div",{className:"min-h-screen",children:[(0,d.jsx)(e.Navigation,{}),(0,d.jsxs)("main",{children:[(0,d.jsx)(g.ContactHero,{}),(0,d.jsx)(h.ContactInfo,{}),(0,d.jsx)(i.ContactFormSection,{}),(0,d.jsx)(j.LocationMap,{})]}),(0,d.jsx)(f.Footer,{})]})}},48450:(a,b,c)=>{"use strict";c.d(b,{ContactFormSection:()=>g});var d=c(60687),e=c(51743),f=c(6946);function g(){return(0,d.jsx)("section",{className:"py-20 bg-cafe-cream",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-12",children:[(0,d.jsx)("h2",{className:"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6",children:"Send Us a Message"}),(0,d.jsx)("p",{className:"text-xl text-cafe-brown max-w-2xl mx-auto leading-relaxed",children:"Have a question, suggestion, or just want to say hello? We'd love to hear from you. Fill out the form below and we'll get back to you within 24 hours."})]}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},children:(0,d.jsx)(f.D,{})})]})})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65412:(a,b,c)=>{"use strict";c.d(b,{ContactInfo:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ContactInfo() from the server but ContactInfo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact\\contact-info.tsx","ContactInfo")},65891:(a,b,c)=>{"use strict";c.d(b,{ContactFormSection:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ContactFormSection() from the server but ContactFormSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact\\contact-form-section.tsx","ContactFormSection")},71074:(a,b,c)=>{Promise.resolve().then(c.bind(c,48450)),Promise.resolve().then(c.bind(c,78286)),Promise.resolve().then(c.bind(c,30262)),Promise.resolve().then(c.bind(c,11938)),Promise.resolve().then(c.bind(c,94101)),Promise.resolve().then(c.bind(c,14246))},78286:(a,b,c)=>{"use strict";c.d(b,{ContactHero:()=>k});var d=c(60687),e=c(51743),f=c(97992),g=c(48340),h=c(19169),i=c(48730),j=c(40846);function k(){return(0,d.jsxs)("section",{className:"relative min-h-[70vh] flex items-center justify-center bg-cafe-pattern pt-16",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-cover bg-center bg-no-repeat",style:{backgroundImage:"url('https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=1920&h=1080&fit=crop')",backgroundBlendMode:"overlay"},children:(0,d.jsx)("div",{className:"absolute inset-0 bg-cafe-dark-brown/60"})}),(0,d.jsx)("div",{className:"relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white",children:(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"space-y-8",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(e.P.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.6,delay:.2},className:"flex justify-center",children:(0,d.jsx)(f.A,{className:"h-16 w-16 text-cafe-gold"})}),(0,d.jsx)("h1",{className:"font-serif text-5xl md:text-6xl font-bold",children:"Visit Us Today"}),(0,d.jsx)("p",{className:"text-xl md:text-2xl text-cafe-cream font-medium max-w-3xl mx-auto",children:"We're Located in the Heart of Biratnagar - Come Experience the Warmth of Himalayan Hospitality"})]}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.6},className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-16",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(f.A,{className:"h-8 w-8 text-cafe-gold"})}),(0,d.jsx)("div",{className:"text-sm font-semibold text-cafe-gold mb-2",children:"ADDRESS"}),(0,d.jsxs)("div",{className:"text-cafe-cream text-sm leading-relaxed",children:["Main Road, Biratnagar-13",(0,d.jsx)("br",{}),"Morang, Nepal"]})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(g.A,{className:"h-8 w-8 text-cafe-gold"})}),(0,d.jsx)("div",{className:"text-sm font-semibold text-cafe-gold mb-2",children:"PHONE"}),(0,d.jsx)("div",{className:"text-cafe-cream",children:j.I.contact.phone})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(h.A,{className:"h-8 w-8 text-cafe-gold"})}),(0,d.jsx)("div",{className:"text-sm font-semibold text-cafe-gold mb-2",children:"EMAIL"}),(0,d.jsx)("div",{className:"text-cafe-cream",children:j.I.contact.email})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(i.A,{className:"h-8 w-8 text-cafe-gold"})}),(0,d.jsx)("div",{className:"text-sm font-semibold text-cafe-gold mb-2",children:"HOURS"}),(0,d.jsxs)("div",{className:"text-cafe-cream text-sm",children:["Mon-Fri: 6AM-10PM",(0,d.jsx)("br",{}),"Weekends: 7AM-11PM"]})]})]}),(0,d.jsx)(e.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.6,delay:1},className:"pt-8",children:(0,d.jsx)("p",{className:"text-cafe-cream/80 text-sm",children:"Get directions, hours, and contact information below"})})]})})]})}},81128:(a,b,c)=>{"use strict";c.d(b,{ContactHero:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ContactHero() from the server but ContactHero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact\\contact-hero.tsx","ContactHero")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},92200:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("navigation",[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]])},96713:(a,b,c)=>{"use strict";c.d(b,{LocationMap:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call LocationMap() from the server but LocationMap is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\contact\\location-map.tsx","LocationMap")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,453,953,548,180,946],()=>b(b.s=8859));module.exports=c})();