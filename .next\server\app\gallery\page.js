(()=>{var a={};a.id=235,a.ids=[235],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12595:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["gallery",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,23709)),"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\gallery\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\gallery\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/gallery/page",pathname:"/gallery",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/gallery/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},13966:(a,b,c)=>{"use strict";c.d(b,{GalleryGrid:()=>r});var d=c(60687),e=c(43210),f=c(51743),g=c(88920),h=c(29523),i=c(63503),j=c(96834),k=c(80462),l=c(51361),m=c(62688);let n=(0,m.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),o=(0,m.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var p=c(11860),q=c(78124);function r(){let[a,b]=(0,e.useState)("all"),[c,m]=(0,e.useState)(!1),[r,s]=(0,e.useState)(null),t="all"===a?q.FF:(0,q.c3)(a),u=()=>{s(null)},v=a=>{null!==r&&s("prev"===a?(r-1+t.length)%t.length:(r+1)%t.length)},w=null!==r?t[r]:null;return(0,d.jsx)("section",{className:"py-20 bg-white",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)(f.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-12",children:[(0,d.jsx)("h2",{className:"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6",children:"Moments & Memories"}),(0,d.jsx)("p",{className:"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed",children:"Every photo tells a story of connection, flavor, and the warm hospitality that makes Himalayan Brew special."})]}),(0,d.jsxs)(f.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"mb-12",children:[(0,d.jsx)("div",{className:"md:hidden mb-4",children:(0,d.jsxs)(h.$,{variant:"outline",onClick:()=>m(!c),className:"w-full border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream",children:[(0,d.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Filter by Category"]})}),(0,d.jsx)("div",{className:`${c?"block":"hidden"} md:block`,children:(0,d.jsx)("div",{className:"flex flex-wrap justify-center gap-3",children:q.ZA.map(c=>(0,d.jsx)(h.$,{variant:a===c.key?"default":"outline",onClick:()=>b(c.key),className:`${a===c.key?"bg-cafe-brown text-cafe-cream hover:bg-cafe-dark-brown":"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream"} transition-colors duration-200`,children:c.label},c.key))})})]}),(0,d.jsx)(g.N,{mode:"wait",children:(0,d.jsx)(f.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.4},className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:t.map((a,b)=>(0,d.jsx)(f.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5,delay:.05*b},className:`relative group cursor-pointer overflow-hidden rounded-lg ${b%7==0?"md:col-span-2 md:row-span-2":b%5==0?"lg:col-span-2":""}`,children:(0,d.jsx)(i.lG,{children:(0,d.jsx)(i.zM,{asChild:!0,children:(0,d.jsxs)("div",{className:"relative aspect-square overflow-hidden rounded-lg",onClick:()=>{s(b)},children:[(0,d.jsx)("img",{src:a.src,alt:a.alt,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"}),(0,d.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-colors duration-300 flex items-center justify-center",children:(0,d.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,d.jsx)("div",{className:"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center",children:(0,d.jsx)(l.A,{className:"h-6 w-6 text-white"})})})}),(0,d.jsx)("div",{className:"absolute top-3 left-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,d.jsx)(j.E,{className:"bg-cafe-brown/80 text-cafe-cream text-xs backdrop-blur-sm",children:a.category})}),(0,d.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,d.jsx)("h3",{className:"text-white font-semibold text-sm",children:a.title})})]})})})},a.id))},a)}),w&&(0,d.jsx)(i.lG,{open:null!==r,onOpenChange:u,children:(0,d.jsx)(i.Cf,{className:"max-w-6xl w-full p-0 border-0 bg-transparent",children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("img",{src:w.src,alt:w.alt,className:"w-full h-auto max-h-[80vh] object-contain rounded-lg"}),(0,d.jsx)(h.$,{variant:"ghost",size:"sm",onClick:()=>v("prev"),className:"absolute left-4 top-1/2 -translate-y-1/2 bg-black/20 hover:bg-black/40 text-white border-0",children:(0,d.jsx)(n,{className:"h-6 w-6"})}),(0,d.jsx)(h.$,{variant:"ghost",size:"sm",onClick:()=>v("next"),className:"absolute right-4 top-1/2 -translate-y-1/2 bg-black/20 hover:bg-black/40 text-white border-0",children:(0,d.jsx)(o,{className:"h-6 w-6"})}),(0,d.jsx)(h.$,{variant:"ghost",size:"sm",onClick:u,className:"absolute top-4 right-4 bg-black/20 hover:bg-black/40 text-white border-0",children:(0,d.jsx)(p.A,{className:"h-6 w-6"})}),(0,d.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6 rounded-b-lg",children:[(0,d.jsx)("h3",{className:"text-white font-serif text-xl font-semibold mb-2",children:w.title}),w.description&&(0,d.jsx)("p",{className:"text-white/90 text-sm",children:w.description}),(0,d.jsxs)("div",{className:"flex items-center gap-2 mt-3",children:[(0,d.jsx)(j.E,{className:"bg-cafe-brown text-cafe-cream",children:w.category}),(0,d.jsxs)("span",{className:"text-white/70 text-sm",children:[r+1," of ",t.length]})]})]})]})})}),(0,d.jsx)(f.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.4,delay:.2},className:"text-center mt-12",children:(0,d.jsxs)("p",{className:"text-cafe-brown",children:["Showing ",t.length," ",1===t.length?"photo":"photos","all"!==a&&` in ${q.ZA.find(b=>b.key===a)?.label}`]})})]})})}},14163:(a,b,c)=>{"use strict";c.d(b,{hO:()=>i,sG:()=>h});var d=c(43210),e=c(51215),f=c(8730),g=c(60687),h=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,b)=>{let c=(0,f.TL)(`Primitive.${b}`),e=d.forwardRef((a,d)=>{let{asChild:e,...f}=a;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,g.jsx)(e?c:b,{...f,ref:d})});return e.displayName=`Primitive.${b}`,{...a,[b]:e}},{});function i(a,b){a&&e.flushSync(()=>a.dispatchEvent(b))}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23709:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(37413),e=c(64544),f=c(64947),g=c(65912),h=c(57040);let i={title:"Gallery - Himalayan Brew Caf\xe9 | Photos of Our Caf\xe9, Food & Community",description:"Browse our photo gallery showcasing the cozy atmosphere, delicious food, and vibrant community at Himalayan Brew Caf\xe9 in Biratnagar, Nepal.",keywords:"gallery Himalayan Brew, caf\xe9 photos Biratnagar, food photography, caf\xe9 interior, Nepal caf\xe9 gallery"};function j(){return(0,d.jsxs)("div",{className:"min-h-screen",children:[(0,d.jsx)(e.Navigation,{}),(0,d.jsxs)("main",{children:[(0,d.jsx)(g.GalleryHero,{}),(0,d.jsx)(h.GalleryGrid,{})]}),(0,d.jsx)(f.Footer,{})]})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31395:(a,b,c)=>{Promise.resolve().then(c.bind(c,94101)),Promise.resolve().then(c.bind(c,13966)),Promise.resolve().then(c.bind(c,88404)),Promise.resolve().then(c.bind(c,14246))},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},48427:(a,b,c)=>{Promise.resolve().then(c.bind(c,64947)),Promise.resolve().then(c.bind(c,57040)),Promise.resolve().then(c.bind(c,65912)),Promise.resolve().then(c.bind(c,64544))},57040:(a,b,c)=>{"use strict";c.d(b,{GalleryGrid:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call GalleryGrid() from the server but GalleryGrid is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\gallery\\gallery-grid.tsx","GalleryGrid")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(a,b,c)=>{"use strict";c.d(b,{Cf:()=>l,lG:()=>h,zM:()=>i});var d=c(60687);c(43210);var e=c(97022),f=c(11860),g=c(4780);function h({...a}){return(0,d.jsx)(e.bL,{"data-slot":"dialog",...a})}function i({...a}){return(0,d.jsx)(e.l9,{"data-slot":"dialog-trigger",...a})}function j({...a}){return(0,d.jsx)(e.ZL,{"data-slot":"dialog-portal",...a})}function k({className:a,...b}){return(0,d.jsx)(e.hJ,{"data-slot":"dialog-overlay",className:(0,g.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...b})}function l({className:a,children:b,showCloseButton:c=!0,...h}){return(0,d.jsxs)(j,{"data-slot":"dialog-portal",children:[(0,d.jsx)(k,{}),(0,d.jsxs)(e.UC,{"data-slot":"dialog-content",className:(0,g.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...h,children:[b,c&&(0,d.jsxs)(e.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,d.jsx)(f.A,{}),(0,d.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}},65912:(a,b,c)=>{"use strict";c.d(b,{GalleryHero:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call GalleryHero() from the server but GalleryHero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\gallery\\gallery-hero.tsx","GalleryHero")},78124:(a,b,c)=>{"use strict";c.d(b,{FF:()=>d,PR:()=>f,ZA:()=>g,c3:()=>e});let d=[{id:"g1",src:"https://images.unsplash.com/photo-1554118811-1e0d58224f24?w=600&h=400&fit=crop",alt:"Main seating area with cozy wooden furniture",title:"Cozy Main Seating Area",category:"interior",description:"Our warm and inviting main seating area with handcrafted wooden furniture"},{id:"g2",src:"https://images.unsplash.com/photo-1559925393-8be0ec4767c8?w=600&h=400&fit=crop",alt:"Corner reading nook with books and plants",title:"Reading Corner",category:"interior",description:"Perfect corner for book lovers with natural lighting and plants"},{id:"g3",src:"https://images.unsplash.com/photo-1442975631115-c4f7b05b8a2c?w=600&h=400&fit=crop",alt:"Coffee counter with barista equipment",title:"Coffee Counter",category:"interior",description:"Our professional coffee counter where magic happens"},{id:"g4",src:"https://images.unsplash.com/photo-1521017432531-fbd92d768814?w=600&h=400&fit=crop",alt:"Upstairs seating area with mountain views",title:"Upstairs Seating",category:"interior",description:"Second floor seating with beautiful mountain views"},{id:"g5",src:"https://images.unsplash.com/photo-1496116218417-1a781b1c416c?w=600&h=400&fit=crop",alt:"Fresh steamed momos with sauce",title:"Traditional Momos",category:"food",description:"Our signature momos, handmade fresh daily"},{id:"g6",src:"https://images.unsplash.com/photo-1585937421612-70a008356fbe?w=600&h=400&fit=crop",alt:"Traditional dal bhat set meal",title:"Dal Bhat Set",category:"food",description:"Authentic Nepali dal bhat with fresh vegetables"},{id:"g7",src:"https://images.unsplash.com/photo-1555507036-ab794f4afe5e?w=600&h=400&fit=crop",alt:"Fresh baked pastries and bread",title:"Fresh Pastries",category:"food",description:"Daily baked pastries and bread made in-house"},{id:"g8",src:"https://images.unsplash.com/photo-1525351484163-7529414344d8?w=600&h=400&fit=crop",alt:"English breakfast plate",title:"Hearty Breakfast",category:"food",description:"Start your day with our hearty breakfast options"},{id:"g9",src:"https://images.unsplash.com/photo-1514432324607-a09d9b4aefdd?w=600&h=400&fit=crop",alt:"Latte with beautiful coffee art",title:"Coffee Art",category:"drinks",description:"Our baristas create beautiful latte art with every cup"},{id:"g10",src:"https://images.unsplash.com/photo-1571934811356-5cc061b6821f?w=600&h=400&fit=crop",alt:"Traditional masala chai in glass",title:"Masala Chai",category:"drinks",description:"Traditional spiced chai served the authentic way"},{id:"g11",src:"https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=600&h=400&fit=crop",alt:"Iced coffee with condensed milk",title:"Iced Himalayan Coffee",category:"drinks",description:"Our signature coffee served cold and refreshing"},{id:"g12",src:"https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=600&h=400&fit=crop",alt:"Various drinks on wooden table",title:"Drink Selection",category:"drinks",description:"Wide variety of hot and cold beverages"},{id:"g13",src:"https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop",alt:"Friends enjoying coffee together",title:"Friends Gathering",category:"people",description:"Friends enjoying quality time over coffee"},{id:"g14",src:"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=400&fit=crop",alt:"Students studying with laptops",title:"Study Session",category:"people",description:"Students finding the perfect study environment"},{id:"g15",src:"https://images.unsplash.com/photo-1543007630-9710e4a00a20?w=600&h=400&fit=crop",alt:"Family enjoying meal together",title:"Family Time",category:"people",description:"Families creating memories over delicious food"},{id:"g16",src:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=400&fit=crop",alt:"Barista preparing coffee",title:"Our Barista",category:"people",description:"Skilled baristas crafting the perfect cup"},{id:"g17",src:"https://images.unsplash.com/photo-1554118811-1e0d58224f24?w=600&h=400&fit=crop",alt:"Caf\xe9 exterior with signage",title:"Caf\xe9 Exterior",category:"exterior",description:"Welcome to Himalayan Brew Caf\xe9 on Main Road"},{id:"g18",src:"https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=600&h=400&fit=crop",alt:"Outdoor terrace seating",title:"Outdoor Terrace",category:"exterior",description:"Enjoy your coffee in our outdoor terrace area"},{id:"g19",src:"https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?w=600&h=400&fit=crop",alt:"Caf\xe9 in the evening with warm lighting",title:"Evening Ambiance",category:"exterior",description:"Cozy evening atmosphere with warm lighting"},{id:"g20",src:"https://images.unsplash.com/photo-1559925393-8be0ec4767c8?w=600&h=400&fit=crop",alt:"Street view of the caf\xe9 location",title:"Street View",category:"exterior",description:"Located in the heart of Biratnagar's main road"}],e=a=>d.filter(b=>b.category===a),f=d.slice(0,8),g=[{key:"all",label:"All Photos"},{key:"interior",label:"Interior"},{key:"food",label:"Food"},{key:"drinks",label:"Drinks"},{key:"people",label:"People"},{key:"exterior",label:"Exterior"}]},80462:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88404:(a,b,c)=>{"use strict";c.d(b,{GalleryHero:()=>j});var d=c(60687),e=c(51743),f=c(51361);let g=(0,c(62688).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var h=c(13166),i=c(41312);function j(){return(0,d.jsxs)("section",{className:"relative min-h-[70vh] flex items-center justify-center bg-cafe-pattern pt-16",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-cover bg-center bg-no-repeat",style:{backgroundImage:"url('https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=1920&h=1080&fit=crop')",backgroundBlendMode:"overlay"},children:(0,d.jsx)("div",{className:"absolute inset-0 bg-cafe-dark-brown/60"})}),(0,d.jsx)("div",{className:"relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white",children:(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"space-y-8",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(e.P.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.6,delay:.2},className:"flex justify-center",children:(0,d.jsx)(f.A,{className:"h-16 w-16 text-cafe-gold"})}),(0,d.jsx)("h1",{className:"font-serif text-5xl md:text-6xl font-bold",children:"Our Gallery"}),(0,d.jsx)("p",{className:"text-xl md:text-2xl text-cafe-cream font-medium max-w-3xl mx-auto",children:"A Visual Journey Through Our Caf\xe9 - Capturing Moments, Memories, and the Magic of Community"})]}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.6},className:"grid grid-cols-2 md:grid-cols-4 gap-8 mt-16",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(g,{className:"h-8 w-8 text-cafe-gold"})}),(0,d.jsx)("div",{className:"text-3xl font-bold text-cafe-gold mb-2",children:"20+"}),(0,d.jsx)("div",{className:"text-cafe-cream",children:"Photos"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(h.A,{className:"h-8 w-8 text-cafe-gold"})}),(0,d.jsx)("div",{className:"text-3xl font-bold text-cafe-gold mb-2",children:"5"}),(0,d.jsx)("div",{className:"text-cafe-cream",children:"Categories"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(i.A,{className:"h-8 w-8 text-cafe-gold"})}),(0,d.jsx)("div",{className:"text-3xl font-bold text-cafe-gold mb-2",children:"100+"}),(0,d.jsx)("div",{className:"text-cafe-cream",children:"Happy Moments"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(f.A,{className:"h-8 w-8 text-cafe-gold"})}),(0,d.jsx)("div",{className:"text-3xl font-bold text-cafe-gold mb-2",children:"Daily"}),(0,d.jsx)("div",{className:"text-cafe-cream",children:"New Memories"})]})]}),(0,d.jsx)(e.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.6,delay:1},className:"pt-8",children:(0,d.jsx)("p",{className:"text-cafe-cream/80 text-sm",children:"Explore our collection of moments and memories"})})]})})]})}},96834:(a,b,c)=>{"use strict";c.d(b,{E:()=>i});var d=c(60687);c(43210);var e=c(8730),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:a,variant:b,asChild:c=!1,...f}){let i=c?e.DX:"span";return(0,d.jsx)(i,{"data-slot":"badge",className:(0,g.cn)(h({variant:b}),a),...f})}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,453,953,723,180],()=>b(b.s=12595));module.exports=c})();