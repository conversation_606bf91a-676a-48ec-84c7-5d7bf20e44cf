"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Star, Quote, Calendar, User } from "lucide-react";
import { testimonials } from "@/data/testimonials";
import Link from "next/link";
import Image from "next/image";

export function TestimonialsGrid() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6">
            Stories from Our Community
          </h2>
          <p className="text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed">
            Every review represents a moment, a memory, and a connection made at Himalayan Brew. Here are their stories in their own words.
          </p>
        </motion.div>

        {/* Testimonials Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full border-cafe-beige hover:shadow-cafe-lg transition-all duration-300 group">
                <CardContent className="p-6 space-y-6">
                  {/* Quote Icon */}
                  <div className="flex justify-center">
                    <div className="w-12 h-12 bg-cafe-gold/10 rounded-full flex items-center justify-center group-hover:bg-cafe-gold/20 transition-colors duration-300">
                      <Quote className="h-6 w-6 text-cafe-gold" />
                    </div>
                  </div>

                  {/* Rating */}
                  <div className="flex justify-center gap-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 fill-cafe-gold text-cafe-gold" />
                    ))}
                  </div>

                  {/* Content */}
                  <blockquote className="text-cafe-brown leading-relaxed text-center italic">
                    &ldquo;{testimonial.content}&rdquo;
                  </blockquote>

                  {/* Author */}
                  <div className="text-center pt-4 border-t border-cafe-beige">
                    <div className="w-16 h-16 mx-auto mb-3 rounded-full overflow-hidden shadow-cafe">
                      <Image
                        src={testimonial.image}
                        alt={testimonial.name}
                        width={64}
                        height={64}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                    </div>
                    <h4 className="font-serif font-semibold text-cafe-dark-brown">
                      {testimonial.name}
                    </h4>
                    <p className="text-sm text-cafe-brown mb-2">
                      {testimonial.role}
                    </p>
                    <div className="flex items-center justify-center gap-2 text-xs text-cafe-brown">
                      <Calendar className="h-3 w-3" />
                      <span>{new Date(testimonial.date).toLocaleDateString()}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <Card className="border-cafe-beige bg-cafe-cream shadow-cafe-lg max-w-4xl mx-auto">
            <CardContent className="p-12">
              <div className="w-16 h-16 bg-cafe-brown/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <User className="h-8 w-8 text-cafe-brown" />
              </div>
              <h3 className="font-serif text-3xl font-bold text-cafe-dark-brown mb-6">
                Share Your Experience
              </h3>
              <p className="text-xl text-cafe-brown leading-relaxed mb-8 max-w-2xl mx-auto">
                Have you visited Himalayan Brew? We&apos;d love to hear about your experience! Your feedback helps us continue to improve and serve our community better.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/contact">
                  <Button 
                    size="lg" 
                    className="bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream px-8 py-3 text-lg font-semibold shadow-cafe-lg"
                  >
                    Leave a Review
                  </Button>
                </Link>
                <Link href="/menu">
                  <Button 
                    variant="outline" 
                    size="lg"
                    className="border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream px-8 py-3 text-lg font-semibold"
                  >
                    View Our Menu
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Social Proof */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="text-2xl font-bold text-cafe-dark-brown mb-1">500+</div>
              <div className="text-cafe-brown text-sm">Satisfied Customers</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-cafe-dark-brown mb-1">4.9★</div>
              <div className="text-cafe-brown text-sm">Average Rating</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-cafe-dark-brown mb-1">3+</div>
              <div className="text-cafe-brown text-sm">Years Serving</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-cafe-dark-brown mb-1">100%</div>
              <div className="text-cafe-brown text-sm">Recommended</div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
