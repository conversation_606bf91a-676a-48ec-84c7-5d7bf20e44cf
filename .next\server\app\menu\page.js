(()=>{var a={};a.id=904,a.ids=[904],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10996:(a,b,c)=>{"use strict";c.d(b,{MenuCategories:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call MenuCategories() from the server but MenuCategories is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\menu\\menu-categories.tsx","MenuCategories")},11871:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["menu",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,38688)),"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\menu\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\menu\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/menu/page",pathname:"/menu",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/menu/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},12822:(a,b,c)=>{"use strict";c.d(b,{MenuCategories:()=>o});var d=c(60687),e=c(43210),f=c(51743),g=c(88920),h=c(44493),i=c(29523),j=c(96834),k=c(80462),l=c(64398),m=c(54082),n=c(36967);function o(){let[a,b]=(0,e.useState)("All"),[c,o]=(0,e.useState)(!1),p="All"===a?n.W7:(0,n.uQ)(a),q=["All",...n.Gr];return(0,d.jsx)("section",{className:"py-20 bg-white",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)(f.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-12",children:[(0,d.jsx)("h2",{className:"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6",children:"Explore Our Menu"}),(0,d.jsx)("p",{className:"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed",children:"Every dish and drink is crafted with love, using the finest ingredients and traditional recipes passed down through generations."})]}),(0,d.jsxs)(f.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"mb-12",children:[(0,d.jsx)("div",{className:"md:hidden mb-4",children:(0,d.jsxs)(i.$,{variant:"outline",onClick:()=>o(!c),className:"w-full border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream",children:[(0,d.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Filter by Category"]})}),(0,d.jsx)("div",{className:`${c?"block":"hidden"} md:block`,children:(0,d.jsx)("div",{className:"flex flex-wrap justify-center gap-3",children:q.map(c=>(0,d.jsx)(i.$,{variant:a===c?"default":"outline",onClick:()=>b(c),className:`${a===c?"bg-cafe-brown text-cafe-cream hover:bg-cafe-dark-brown":"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream"} transition-colors duration-200`,children:c},c))})})]}),(0,d.jsx)(g.N,{mode:"wait",children:(0,d.jsx)(f.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.4},className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:p.map((a,b)=>(0,d.jsx)(f.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*b},children:(0,d.jsxs)(h.Zp,{className:"h-full overflow-hidden border-cafe-beige hover:shadow-cafe-lg transition-all duration-300 group",children:[(0,d.jsxs)("div",{className:"relative aspect-[4/3] overflow-hidden",children:[(0,d.jsx)("img",{src:a.image,alt:a.name,className:"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"}),(0,d.jsxs)("div",{className:"absolute top-4 left-4 flex gap-2",children:[a.isPopular&&(0,d.jsxs)(j.E,{className:"bg-cafe-gold text-cafe-dark-brown font-semibold",children:[(0,d.jsx)(l.A,{className:"h-3 w-3 mr-1"}),"Popular"]}),a.isVegetarian&&(0,d.jsxs)(j.E,{variant:"secondary",className:"bg-cafe-green text-white",children:[(0,d.jsx)(m.A,{className:"h-3 w-3 mr-1"}),"Veg"]}),a.isVegan&&(0,d.jsxs)(j.E,{variant:"secondary",className:"bg-cafe-dark-green text-white",children:[(0,d.jsx)(m.A,{className:"h-3 w-3 mr-1"}),"Vegan"]})]}),(0,d.jsx)("div",{className:"absolute bottom-4 right-4",children:(0,d.jsxs)("div",{className:"bg-cafe-dark-brown/80 backdrop-blur-sm text-cafe-cream px-3 py-1 rounded-full font-bold",children:["Rs. ",a.price]})})]}),(0,d.jsx)(h.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex justify-between items-start",children:[(0,d.jsx)("h3",{className:"font-serif text-xl font-semibold text-cafe-dark-brown",children:a.name}),(0,d.jsx)(j.E,{variant:"outline",className:"border-cafe-beige text-cafe-brown text-xs",children:a.category})]}),(0,d.jsx)("p",{className:"text-cafe-brown leading-relaxed text-sm",children:a.description})]})})]})},a.id))},a)}),(0,d.jsx)(f.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.4,delay:.2},className:"text-center mt-12",children:(0,d.jsxs)("p",{className:"text-cafe-brown",children:["Showing ",p.length," ",1===p.length?"item":"items","All"!==a&&` in ${a}`]})})]})})}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19792:(a,b,c)=>{"use strict";c.d(b,{MenuCTA:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call MenuCTA() from the server but MenuCTA is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\menu\\menu-cta.tsx","MenuCTA")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29731:(a,b,c)=>{Promise.resolve().then(c.bind(c,64947)),Promise.resolve().then(c.bind(c,10996)),Promise.resolve().then(c.bind(c,19792)),Promise.resolve().then(c.bind(c,82704)),Promise.resolve().then(c.bind(c,64544))},33873:a=>{"use strict";a.exports=require("path")},36967:(a,b,c)=>{"use strict";c.d(b,{Gr:()=>d,W7:()=>e,mZ:()=>f,uQ:()=>g});let d=["Hot Drinks","Cold Drinks","Snacks","Bakery","Traditional Nepali","Breakfast"],e=[{id:"hd1",name:"Himalayan Coffee",description:"Our signature blend from Ilam hills, rich and aromatic with notes of chocolate and nuts",price:180,image:"https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=400&h=300&fit=crop",category:"Hot Drinks",isPopular:!0},{id:"hd2",name:"Masala Chai",description:"Traditional spiced tea with cardamom, cinnamon, and ginger, served with milk",price:120,image:"https://images.unsplash.com/photo-1571934811356-5cc061b6821f?w=400&h=300&fit=crop",category:"Hot Drinks",isPopular:!0,isVegetarian:!0},{id:"hd3",name:"Butter Tea (Po Cha)",description:"Traditional Tibetan butter tea, perfect for cold Biratnagar mornings",price:150,image:"https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=300&fit=crop",category:"Hot Drinks",isVegetarian:!0},{id:"hd4",name:"Cappuccino",description:"Classic Italian coffee with steamed milk foam and a sprinkle of cocoa",price:200,image:"https://images.unsplash.com/photo-1572442388796-11668a67e53d?w=400&h=300&fit=crop",category:"Hot Drinks",isVegetarian:!0},{id:"hd5",name:"Hot Chocolate",description:"Rich and creamy hot chocolate topped with whipped cream",price:220,image:"https://images.unsplash.com/photo-1542990253-0d0f5be5f0ed?w=400&h=300&fit=crop",category:"Hot Drinks",isVegetarian:!0},{id:"cd1",name:"Iced Himalayan Coffee",description:"Our signature coffee served cold with ice and a touch of condensed milk",price:200,image:"https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=400&h=300&fit=crop",category:"Cold Drinks",isPopular:!0},{id:"cd2",name:"Mango Lassi",description:"Creamy yogurt drink blended with fresh mango and cardamom",price:180,image:"https://images.unsplash.com/photo-1553909489-cd47e0ef937f?w=400&h=300&fit=crop",category:"Cold Drinks",isVegetarian:!0},{id:"cd3",name:"Fresh Lime Soda",description:"Refreshing lime juice with soda water and mint leaves",price:120,image:"https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=400&h=300&fit=crop",category:"Cold Drinks",isVegan:!0},{id:"cd4",name:"Iced Chai Latte",description:"Spiced chai served cold with milk and ice",price:160,image:"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop",category:"Cold Drinks",isVegetarian:!0},{id:"sn1",name:"Chicken Momo",description:"Traditional Nepali dumplings filled with seasoned chicken, served with spicy sauce",price:280,image:"https://images.unsplash.com/photo-1496116218417-1a781b1c416c?w=400&h=300&fit=crop",category:"Snacks",isPopular:!0},{id:"sn2",name:"Vegetable Momo",description:"Steamed dumplings filled with fresh vegetables and herbs",price:240,image:"https://images.unsplash.com/photo-1563379091339-03246963d96a?w=400&h=300&fit=crop",category:"Snacks",isVegetarian:!0,isPopular:!0},{id:"sn3",name:"Samosa Chat",description:"Crispy samosas topped with yogurt, chutneys, and spices",price:160,image:"https://images.unsplash.com/photo-1601050690597-df0568f70950?w=400&h=300&fit=crop",category:"Snacks",isVegetarian:!0},{id:"sn4",name:"French Fries",description:"Golden crispy fries served with ketchup and mayo",price:180,image:"https://images.unsplash.com/photo-1573080496219-bb080dd4f877?w=400&h=300&fit=crop",category:"Snacks",isVegetarian:!0},{id:"bk1",name:"Chocolate Croissant",description:"Buttery croissant filled with rich dark chocolate",price:150,image:"https://images.unsplash.com/photo-1555507036-ab794f4afe5e?w=400&h=300&fit=crop",category:"Bakery",isVegetarian:!0},{id:"bk2",name:"Banana Bread",description:"Homemade banana bread with walnuts, perfect with coffee",price:120,image:"https://images.unsplash.com/photo-1586985289688-ca3cf47d3e6e?w=400&h=300&fit=crop",category:"Bakery",isVegetarian:!0,isPopular:!0},{id:"bk3",name:"Blueberry Muffin",description:"Fresh baked muffin loaded with juicy blueberries",price:140,image:"https://images.unsplash.com/photo-1607958996333-41aef7caefaa?w=400&h=300&fit=crop",category:"Bakery",isVegetarian:!0},{id:"tn1",name:"Dal Bhat Set",description:"Traditional Nepali meal with lentil soup, rice, vegetables, and pickle",price:350,image:"https://images.unsplash.com/photo-1585937421612-70a008356fbe?w=400&h=300&fit=crop",category:"Traditional Nepali",isVegetarian:!0,isPopular:!0},{id:"tn2",name:"Sel Roti",description:"Traditional ring-shaped rice bread, crispy outside and soft inside",price:80,image:"https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=300&fit=crop",category:"Traditional Nepali",isVegetarian:!0},{id:"bf1",name:"English Breakfast",description:"Eggs, toast, baked beans, and hash browns with coffee",price:420,image:"https://images.unsplash.com/photo-1525351484163-7529414344d8?w=400&h=300&fit=crop",category:"Breakfast",isVegetarian:!0},{id:"bf2",name:"Pancakes",description:"Fluffy pancakes served with maple syrup and butter",price:280,image:"https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400&h=300&fit=crop",category:"Breakfast",isVegetarian:!0}],f=e.filter(a=>a.isPopular),g=a=>e.filter(b=>b.category===a)},38688:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>k,metadata:()=>j});var d=c(37413),e=c(64544),f=c(64947),g=c(82704),h=c(10996),i=c(19792);let j={title:"Menu - Himalayan Brew Caf\xe9 | Coffee, Food & Traditional Nepali Cuisine",description:"Explore our full menu featuring premium coffee, traditional Nepali dishes like momos and dal bhat, fresh pastries, and more at Himalayan Brew Caf\xe9 in Biratnagar.",keywords:"menu Himalayan Brew, coffee menu Biratnagar, momos Biratnagar, dal bhat, Nepali food menu, caf\xe9 menu Nepal"};function k(){return(0,d.jsxs)("div",{className:"min-h-screen",children:[(0,d.jsx)(e.Navigation,{}),(0,d.jsxs)("main",{children:[(0,d.jsx)(g.MenuHero,{}),(0,d.jsx)(h.MenuCategories,{}),(0,d.jsx)(i.MenuCTA,{})]}),(0,d.jsx)(f.Footer,{})]})}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},43803:(a,b,c)=>{Promise.resolve().then(c.bind(c,94101)),Promise.resolve().then(c.bind(c,12822)),Promise.resolve().then(c.bind(c,83902)),Promise.resolve().then(c.bind(c,60686)),Promise.resolve().then(c.bind(c,14246))},44493:(a,b,c)=>{"use strict";c.d(b,{Wu:()=>i,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},54082:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},60686:(a,b,c)=>{"use strict";c.d(b,{MenuHero:()=>j});var d=c(60687),e=c(51743);let f=(0,c(62688).A)("utensils",[["path",{d:"M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2",key:"cjf0a3"}],["path",{d:"M7 2v20",key:"1473qp"}],["path",{d:"M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7",key:"j28e5"}]]);var g=c(13166),h=c(64398),i=c(48730);function j(){return(0,d.jsxs)("section",{className:"relative min-h-[70vh] flex items-center justify-center bg-cafe-pattern pt-16",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-cover bg-center bg-no-repeat",style:{backgroundImage:"url('https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=1920&h=1080&fit=crop')",backgroundBlendMode:"overlay"},children:(0,d.jsx)("div",{className:"absolute inset-0 bg-cafe-dark-brown/70"})}),(0,d.jsx)("div",{className:"relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white",children:(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"space-y-8",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(e.P.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.6,delay:.2},className:"flex justify-center",children:(0,d.jsx)(f,{className:"h-16 w-16 text-cafe-gold"})}),(0,d.jsx)("h1",{className:"font-serif text-5xl md:text-6xl font-bold",children:"Our Menu"}),(0,d.jsx)("p",{className:"text-xl md:text-2xl text-cafe-cream font-medium max-w-3xl mx-auto",children:"From Premium Coffee to Authentic Nepali Cuisine - Discover Flavors That Tell a Story"})]}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.6},className:"grid grid-cols-2 md:grid-cols-4 gap-8 mt-16",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(g.A,{className:"h-8 w-8 text-cafe-gold"})}),(0,d.jsx)("div",{className:"text-3xl font-bold text-cafe-gold mb-2",children:"15+"}),(0,d.jsx)("div",{className:"text-cafe-cream",children:"Coffee Varieties"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(f,{className:"h-8 w-8 text-cafe-gold"})}),(0,d.jsx)("div",{className:"text-3xl font-bold text-cafe-gold mb-2",children:"25+"}),(0,d.jsx)("div",{className:"text-cafe-cream",children:"Food Items"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(h.A,{className:"h-8 w-8 text-cafe-gold"})}),(0,d.jsx)("div",{className:"text-3xl font-bold text-cafe-gold mb-2",children:"10+"}),(0,d.jsx)("div",{className:"text-cafe-cream",children:"Signature Dishes"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(i.A,{className:"h-8 w-8 text-cafe-gold"})}),(0,d.jsx)("div",{className:"text-3xl font-bold text-cafe-gold mb-2",children:"6AM"}),(0,d.jsx)("div",{className:"text-cafe-cream",children:"Fresh Daily"})]})]}),(0,d.jsx)(e.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.6,delay:1},className:"pt-8",children:(0,d.jsx)("p",{className:"text-cafe-cream/80 text-sm",children:"Scroll down to explore our full menu"})})]})})]})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},80428:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]])},80462:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},82704:(a,b,c)=>{"use strict";c.d(b,{MenuHero:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call MenuHero() from the server but MenuHero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\menu\\menu-hero.tsx","MenuHero")},83902:(a,b,c)=>{"use strict";c.d(b,{MenuCTA:()=>o});var d=c(60687),e=c(51743),f=c(44493),g=c(29523),h=c(80428),i=c(97992),j=c(48730),k=c(48340),l=c(85814),m=c.n(l),n=c(40846);function o(){return(0,d.jsx)("section",{className:"py-20 bg-cafe-cream",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsx)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:(0,d.jsx)(f.Zp,{className:"border-cafe-beige bg-white shadow-cafe-lg max-w-4xl mx-auto",children:(0,d.jsxs)(f.Wu,{className:"p-12",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold rounded-full flex items-center justify-center mx-auto mb-6",children:(0,d.jsx)(h.A,{className:"h-8 w-8 text-cafe-dark-brown"})}),(0,d.jsx)("h2",{className:"font-serif text-3xl md:text-4xl font-bold text-cafe-dark-brown mb-6",children:"Ready to Experience Our Flavors?"}),(0,d.jsx)("p",{className:"text-xl text-cafe-brown leading-relaxed mb-8 max-w-2xl mx-auto",children:"Visit us today and taste the difference that passion, quality ingredients, and authentic recipes make. Every dish tells a story, every cup creates a memory."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)(m(),{href:"/contact",children:(0,d.jsx)(g.$,{size:"lg",className:"bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream px-8 py-3 text-lg font-semibold shadow-cafe-lg",children:"Visit Us Today"})}),(0,d.jsx)(m(),{href:"/gallery",children:(0,d.jsx)(g.$,{variant:"outline",size:"lg",className:"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream px-8 py-3 text-lg font-semibold",children:"View Gallery"})})]})]})})}),(0,d.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,d.jsx)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1},viewport:{once:!0},children:(0,d.jsx)(f.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300 text-center",children:(0,d.jsxs)(f.Wu,{className:"p-6",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-beige rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(i.A,{className:"h-8 w-8 text-cafe-brown"})}),(0,d.jsx)("h3",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-3",children:"Find Us"}),(0,d.jsx)("p",{className:"text-cafe-brown leading-relaxed mb-4",children:n.I.contact.address}),(0,d.jsx)(m(),{href:"/contact",children:(0,d.jsx)(g.$,{variant:"outline",size:"sm",className:"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream",children:"Get Directions"})})]})})}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},children:(0,d.jsx)(f.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300 text-center",children:(0,d.jsxs)(f.Wu,{className:"p-6",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-beige rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(j.A,{className:"h-8 w-8 text-cafe-brown"})}),(0,d.jsx)("h3",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-3",children:"Opening Hours"}),(0,d.jsxs)("div",{className:"text-cafe-brown leading-relaxed mb-4 space-y-1",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:"Mon - Fri:"}),(0,d.jsx)("span",{children:n.I.hours.weekdays})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:"Weekends:"}),(0,d.jsx)("span",{children:n.I.hours.weekends})]})]}),(0,d.jsx)(g.$,{variant:"outline",size:"sm",className:"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream",children:"View All Hours"})]})})}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},viewport:{once:!0},children:(0,d.jsx)(f.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300 text-center",children:(0,d.jsxs)(f.Wu,{className:"p-6",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-beige rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(k.A,{className:"h-8 w-8 text-cafe-brown"})}),(0,d.jsx)("h3",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-3",children:"Call Us"}),(0,d.jsx)("p",{className:"text-cafe-brown leading-relaxed mb-4",children:"Have questions about our menu or want to make a reservation?"}),(0,d.jsx)("a",{href:`tel:${n.I.contact.phone}`,children:(0,d.jsx)(g.$,{variant:"outline",size:"sm",className:"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream",children:n.I.contact.phone})})]})})})]}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"text-center mt-12",children:(0,d.jsx)("p",{className:"text-cafe-brown italic",children:"* Prices may vary. Please ask our staff about daily specials and seasonal items."})})]})})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96834:(a,b,c)=>{"use strict";c.d(b,{E:()=>i});var d=c(60687);c(43210);var e=c(8730),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:a,variant:b,asChild:c=!1,...f}){let i=c?e.DX:"span";return(0,d.jsx)(i,{"data-slot":"badge",className:(0,g.cn)(h({variant:b}),a),...f})}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,453,953,180],()=>b(b.s=11871));module.exports=c})();