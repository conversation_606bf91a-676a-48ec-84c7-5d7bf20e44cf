"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Coffee, Users, Heart, Award } from "lucide-react";
import Image from "next/image";

const teamMembers = [
  {
    id: "tm1",
    name: "<PERSON><PERSON>",
    role: "Founder & Head Barista",
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face",
    bio: "With over 10 years of experience in the coffee industry, <PERSON><PERSON> founded Himalayan Brew with a vision to bring authentic Nepali coffee culture to Biratnagar.",
    icon: Coffee
  },
  {
    id: "tm2", 
    name: "<PERSON><PERSON>",
    role: "Operations Manager",
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face",
    bio: "<PERSON><PERSON> ensures smooth daily operations and maintains our high standards of service. Her warm personality embodies our commitment to hospitality.",
    icon: Users
  },
  {
    id: "tm3",
    name: "<PERSON><PERSON><PERSON>",
    role: "Head Chef",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face",
    bio: "Arjun brings traditional Nepali flavors to our menu, specializing in authentic momos and dal bhat that remind customers of home.",
    icon: Heart
  },
  {
    id: "tm4",
    name: "Maya Thapa",
    role: "Community Relations",
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300&h=300&fit=crop&crop=face",
    bio: "Maya builds bridges between our café and the Biratnagar community, organizing events and supporting local initiatives.",
    icon: Award
  }
];

export function TeamSection() {
  return (
    <section className="py-20 bg-cafe-beige">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6">
            Meet Our Team
          </h2>
          <p className="text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed">
            Behind every great cup of coffee is a passionate team dedicated to creating exceptional experiences for our community.
          </p>
        </motion.div>

        {/* Team Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {teamMembers.map((member, index) => {
            const IconComponent = member.icon;
            
            return (
              <motion.div
                key={member.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full border-cafe-sand hover:shadow-cafe-lg transition-all duration-300 group bg-white">
                  <CardContent className="p-6 text-center">
                    {/* Profile Image */}
                    <div className="relative mb-6">
                      <div className="w-24 h-24 mx-auto rounded-full overflow-hidden shadow-cafe">
                        <Image
                          src={member.image}
                          alt={member.name}
                          width={96}
                          height={96}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                        />
                      </div>
                      <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-cafe-gold rounded-full flex items-center justify-center shadow-cafe">
                        <IconComponent className="h-4 w-4 text-cafe-dark-brown" />
                      </div>
                    </div>

                    {/* Member Info */}
                    <h3 className="font-serif text-xl font-semibold text-cafe-dark-brown mb-2">
                      {member.name}
                    </h3>
                    <p className="text-cafe-brown font-medium mb-4">
                      {member.role}
                    </p>
                    <p className="text-cafe-brown text-sm leading-relaxed">
                      {member.bio}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Join Our Team CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <Card className="border-cafe-sand bg-white shadow-cafe-lg max-w-2xl mx-auto">
            <CardContent className="p-8">
              <div className="w-16 h-16 bg-cafe-brown/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <Users className="h-8 w-8 text-cafe-brown" />
              </div>
              <h3 className="font-serif text-2xl font-semibold text-cafe-dark-brown mb-4">
                Join Our Family
              </h3>
              <p className="text-cafe-brown leading-relaxed mb-6">
                We&apos;re always looking for passionate individuals who share our love for coffee and community. If you&apos;d like to be part of the Himalayan Brew family, we&apos;d love to hear from you.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center justify-center px-6 py-3 bg-cafe-brown text-cafe-cream rounded-lg font-semibold hover:bg-cafe-dark-brown transition-colors duration-200"
                >
                  Send Your Resume
                </a>
                <a
                  href="/contact"
                  className="inline-flex items-center justify-center px-6 py-3 border border-cafe-brown text-cafe-brown rounded-lg font-semibold hover:bg-cafe-brown hover:text-cafe-cream transition-colors duration-200"
                >
                  Contact Us
                </a>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
}
