{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/navigation.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Menu, X, Coffee } from \"lucide-react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\n\nconst navItems = [\n  { href: \"/\", label: \"Home\" },\n  { href: \"/about\", label: \"About\" },\n  { href: \"/menu\", label: \"Menu\" },\n  { href: \"/gallery\", label: \"Gallery\" },\n  { href: \"/testimonials\", label: \"Testimonials\" },\n  { href: \"/contact\", label: \"Contact\" },\n];\n\nexport function Navigation() {\n  const [isOpen, setIsOpen] = useState(false);\n\n  return (\n    <nav className=\"fixed top-0 left-0 right-0 z-50 bg-cafe-cream/95 backdrop-blur-sm border-b border-cafe-beige\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <Coffee className=\"h-8 w-8 text-cafe-brown\" />\n            <span className=\"font-serif font-bold text-xl text-cafe-dark-brown\">\n              Himalayan Brew\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-cafe-brown hover:text-cafe-dark-brown transition-colors duration-200 font-medium\"\n              >\n                {item.label}\n              </Link>\n            ))}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"text-cafe-brown hover:text-cafe-dark-brown\"\n            >\n              {isOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            className=\"md:hidden bg-cafe-cream border-b border-cafe-beige\"\n          >\n            <div className=\"px-4 py-4 space-y-4\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"block text-cafe-brown hover:text-cafe-dark-brown transition-colors duration-200 font-medium\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  {item.label}\n                </Link>\n              ))}\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AANA;;;;;;;AAQA,MAAM,WAAW;IACf;QAAE,MAAM;QAAK,OAAO;IAAO;IAC3B;QAAE,MAAM;QAAU,OAAO;IAAQ;IACjC;QAAE,MAAM;QAAS,OAAO;IAAO;IAC/B;QAAE,MAAM;QAAY,OAAO;IAAU;IACrC;QAAE,MAAM;QAAiB,OAAO;IAAe;IAC/C;QAAE,MAAM;QAAY,OAAO;IAAU;CACtC;AAEM,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAK,WAAU;8CAAoD;;;;;;;;;;;;sCAMtE,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,KAAK;mCAJN,KAAK,IAAI;;;;;;;;;;sCAUpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,uBAAS,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9D,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,UAAU;0CAExB,KAAK,KAAK;+BALN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAchC", "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/data/cafe-info.ts"], "sourcesContent": ["export const cafeInfo = {\n  name: \"Himalayan Brew Café\",\n  tagline: \"Where Mountains Meet Coffee\",\n  description: \"A cozy corner in the heart of Biratnagar, serving authentic Nepali hospitality with the finest coffee and local delicacies.\",\n  \n  story: `Nestled in the vibrant city of Biratnagar, Himalayan Brew Café was born from a passion for bringing people together over exceptional coffee and warm conversations. \n\nOur journey began in 2020 when our founder, inspired by the rich coffee culture of Nepal's hills and the bustling energy of Biratnagar, decided to create a space that celebrates both tradition and modernity.\n\nWe source our coffee beans directly from the hills of Ilam and Gulmi, supporting local farmers and ensuring every cup tells a story of Nepal's coffee heritage. Our café is more than just a place to grab coffee – it's a community hub where students study, friends catch up, and families create memories.\n\nLocated in the commercial heart of Biratnagar, we've become a beloved gathering spot for locals and visitors alike. Our commitment to quality, sustainability, and community has made us a cornerstone of Biratnagar's growing café culture.`,\n\n  values: [\n    {\n      title: \"Community First\",\n      description: \"We believe in fostering connections and supporting our local community through every cup we serve.\"\n    },\n    {\n      title: \"Quality & Authenticity\",\n      description: \"From bean to cup, we maintain the highest standards while honoring traditional Nepali coffee culture.\"\n    },\n    {\n      title: \"Sustainability\",\n      description: \"We work directly with local farmers and use eco-friendly practices to protect our beautiful Nepal.\"\n    },\n    {\n      title: \"Warm Hospitality\",\n      description: \"Every guest is treated like family, embodying the true spirit of Nepali hospitality.\"\n    }\n  ],\n\n  contact: {\n    address: \"Main Road, Biratnagar-13, Morang, Nepal\",\n    phone: \"+977-21-525-789\",\n    email: \"<EMAIL>\",\n    coordinates: {\n      lat: 26.4525,\n      lng: 87.2718\n    }\n  },\n\n  hours: {\n    weekdays: \"6:00 AM - 10:00 PM\",\n    weekends: \"7:00 AM - 11:00 PM\",\n    holidays: \"8:00 AM - 9:00 PM\"\n  },\n\n  socialMedia: {\n    facebook: \"https://facebook.com/himalayanbrew\",\n    instagram: \"https://instagram.com/himalayanbrew\",\n    twitter: \"https://twitter.com/himalayanbrew\"\n  }\n};\n"], "names": [], "mappings": ";;;AAAO,MAAM,WAAW;IACtB,MAAM;IACN,SAAS;IACT,aAAa;IAEb,OAAO,CAAC;;;;;;4OAMkO,CAAC;IAE3O,QAAQ;QACN;YACE,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,aAAa;QACf;KACD;IAED,SAAS;QACP,SAAS;QACT,OAAO;QACP,OAAO;QACP,aAAa;YACX,KAAK;YACL,KAAK;QACP;IACF;IAEA,OAAO;QACL,UAAU;QACV,UAAU;QACV,UAAU;IACZ;IAEA,aAAa;QACX,UAAU;QACV,WAAW;QACX,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/hero-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Coffee, MapPin, Clock } from \"lucide-react\";\nimport { motion } from \"framer-motion\";\nimport Link from \"next/link\";\nimport { cafeInfo } from \"@/data/cafe-info\";\n\nexport function HeroSection() {\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center bg-cafe-pattern\">\n      {/* Background Image Overlay */}\n      <div\n        className=\"absolute inset-0 bg-cover bg-center bg-no-repeat\"\n        style={{\n          backgroundImage: \"url('https://images.unsplash.com/photo-1554118811-1e0d58224f24?w=1920&h=1080&fit=crop')\",\n          backgroundBlendMode: \"overlay\",\n        }}\n      >\n        <div className=\"absolute inset-0 bg-cafe-cream/80\"></div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"space-y-8\"\n        >\n          {/* Logo and Title */}\n          <div className=\"space-y-4\">\n            <motion.div\n              initial={{ scale: 0.8, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              className=\"flex justify-center\"\n            >\n              <Coffee className=\"h-16 w-16 text-cafe-brown\" />\n            </motion.div>\n            \n            <h1 className=\"font-serif text-5xl md:text-7xl font-bold text-cafe-dark-brown\">\n              {cafeInfo.name}\n            </h1>\n            \n            <p className=\"text-xl md:text-2xl text-cafe-brown font-medium\">\n              {cafeInfo.tagline}\n            </p>\n          </div>\n\n          {/* Description */}\n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n            className=\"max-w-3xl mx-auto text-lg text-cafe-brown leading-relaxed\"\n          >\n            {cafeInfo.description}\n          </motion.p>\n\n          {/* CTA Buttons */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.6 }}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\n          >\n            <Link href=\"/menu\">\n              <Button\n                size=\"lg\"\n                className=\"bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream px-8 py-3 text-lg font-semibold shadow-cafe-lg\"\n              >\n                View Our Menu\n              </Button>\n            </Link>\n            <Link href=\"/gallery\">\n              <Button\n                variant=\"outline\"\n                size=\"lg\"\n                className=\"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream px-8 py-3 text-lg font-semibold\"\n              >\n                Visit Gallery\n              </Button>\n            </Link>\n          </motion.div>\n\n          {/* Quick Info */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.8 }}\n            className=\"flex flex-col md:flex-row gap-8 justify-center items-center text-cafe-brown\"\n          >\n            <div className=\"flex items-center gap-2\">\n              <MapPin className=\"h-5 w-5\" />\n              <span className=\"font-medium\">Main Road, Biratnagar</span>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <Clock className=\"h-5 w-5\" />\n              <span className=\"font-medium\">6:00 AM - 10:00 PM</span>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <Coffee className=\"h-5 w-5\" />\n              <span className=\"font-medium\">Fresh Coffee Daily</span>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 0.6, delay: 1 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <motion.div\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n          className=\"w-6 h-10 border-2 border-cafe-brown rounded-full flex justify-center\"\n        >\n          <motion.div\n            animate={{ y: [0, 12, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n            className=\"w-1 h-3 bg-cafe-brown rounded-full mt-2\"\n          />\n        </motion.div>\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB;oBACjB,qBAAqB;gBACvB;0BAEA,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,OAAO;wCAAK,SAAS;oCAAE;oCAClC,SAAS;wCAAE,OAAO;wCAAG,SAAS;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAGpB,8OAAC;oCAAG,WAAU;8CACX,2HAAA,CAAA,WAAQ,CAAC,IAAI;;;;;;8CAGhB,8OAAC;oCAAE,WAAU;8CACV,2HAAA,CAAA,WAAQ,CAAC,OAAO;;;;;;;;;;;;sCAKrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAET,2HAAA,CAAA,WAAQ,CAAC,WAAW;;;;;;sCAIvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAOL,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;;8CAEhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;;8CAEhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAE;gBACtC,WAAU;0BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;oBAC5C,WAAU;8BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBAAC;wBACzB,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;wBAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMtB", "debugId": null}}, {"offset": {"line": 701, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 796, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/about-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Heart, Users, Leaf, Award } from \"lucide-react\";\nimport { cafeInfo } from \"@/data/cafe-info\";\nimport Image from \"next/image\";\n\nconst valueIcons = {\n  \"Community First\": Users,\n  \"Quality & Authenticity\": Award,\n  \"Sustainability\": Leaf,\n  \"Warm Hospitality\": Heart,\n};\n\nexport function AboutSection() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6\">\n            About Our Story\n          </h2>\n          <p className=\"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed\">\n            Discover the heart and soul behind Himalayan Brew Café, where every cup tells a story of passion, community, and authentic Nepali hospitality.\n          </p>\n        </motion.div>\n\n        {/* Story Content */}\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center mb-20\">\n          {/* Text Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"space-y-6\"\n          >\n            <h3 className=\"font-serif text-3xl font-semibold text-cafe-dark-brown\">\n              Our Journey\n            </h3>\n            <div className=\"prose prose-lg text-cafe-brown\">\n              <p className=\"leading-relaxed\">\n                Nestled in the vibrant city of Biratnagar, Himalayan Brew Café was born from a passion for bringing people together over exceptional coffee and warm conversations.\n              </p>\n              <p className=\"leading-relaxed\">\n                Our journey began in 2020 when our founder, inspired by the rich coffee culture of Nepal&apos;s hills and the bustling energy of Biratnagar, decided to create a space that celebrates both tradition and modernity.\n              </p>\n              <p className=\"leading-relaxed\">\n                We source our coffee beans directly from the hills of Ilam and Gulmi, supporting local farmers and ensuring every cup tells a story of Nepal&apos;s coffee heritage.\n              </p>\n            </div>\n          </motion.div>\n\n          {/* Image */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"relative\"\n          >\n            <div className=\"aspect-square rounded-2xl overflow-hidden shadow-cafe-lg\">\n              <Image\n                src=\"https://images.unsplash.com/photo-1559925393-8be0ec4767c8?w=600&h=600&fit=crop\"\n                alt=\"Himalayan Brew Café story\"\n                width={600}\n                height={600}\n                className=\"w-full h-full object-cover\"\n              />\n            </div>\n            <div className=\"absolute -bottom-6 -right-6 w-24 h-24 bg-cafe-gold rounded-full flex items-center justify-center shadow-cafe-lg\">\n              <Heart className=\"h-12 w-12 text-cafe-dark-brown\" />\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Values Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-12\"\n        >\n          <h3 className=\"font-serif text-3xl font-semibold text-cafe-dark-brown mb-4\">\n            Our Values\n          </h3>\n          <p className=\"text-lg text-cafe-brown max-w-2xl mx-auto\">\n            These core values guide everything we do, from sourcing our beans to serving our community.\n          </p>\n        </motion.div>\n\n        {/* Values Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          {cafeInfo.values.map((value, index) => {\n            const IconComponent = valueIcons[value.title as keyof typeof valueIcons];\n            \n            return (\n              <motion.div\n                key={value.title}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n              >\n                <Card className=\"h-full border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300\">\n                  <CardContent className=\"p-6 text-center space-y-4\">\n                    <div className=\"w-16 h-16 bg-cafe-beige rounded-full flex items-center justify-center mx-auto\">\n                      <IconComponent className=\"h-8 w-8 text-cafe-brown\" />\n                    </div>\n                    <h4 className=\"font-serif text-xl font-semibold text-cafe-dark-brown\">\n                      {value.title}\n                    </h4>\n                    <p className=\"text-cafe-brown leading-relaxed\">\n                      {value.description}\n                    </p>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            );\n          })}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,aAAa;IACjB,mBAAmB,oMAAA,CAAA,QAAK;IACxB,0BAA0B,oMAAA,CAAA,QAAK;IAC/B,kBAAkB,kMAAA,CAAA,OAAI;IACtB,oBAAoB,oMAAA,CAAA,QAAK;AAC3B;AAEO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAsE;;;;;;sCAGpF,8OAAC;4BAAE,WAAU;sCAA4D;;;;;;;;;;;;8BAM3E,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAyD;;;;;;8CAGvE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAkB;;;;;;sDAG/B,8OAAC;4CAAE,WAAU;sDAAkB;;;;;;sDAG/B,8OAAC;4CAAE,WAAU;sDAAkB;;;;;;;;;;;;;;;;;;sCAOnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAGd,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAMvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAA8D;;;;;;sCAG5E,8OAAC;4BAAE,WAAU;sCAA4C;;;;;;;;;;;;8BAM3D,8OAAC;oBAAI,WAAU;8BACZ,2HAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO;wBAC3B,MAAM,gBAAgB,UAAU,CAAC,MAAM,KAAK,CAA4B;wBAExE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAc,WAAU;;;;;;;;;;;sDAE3B,8OAAC;4CAAG,WAAU;sDACX,MAAM,KAAK;;;;;;sDAEd,8OAAC;4CAAE,WAAU;sDACV,MAAM,WAAW;;;;;;;;;;;;;;;;;2BAfnB,MAAM,KAAK;;;;;oBAqBtB;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 1126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/data/menu.ts"], "sourcesContent": ["export interface MenuItem {\n  id: string;\n  name: string;\n  description: string;\n  price: number;\n  image: string;\n  category: string;\n  isPopular?: boolean;\n  isVegetarian?: boolean;\n  isVegan?: boolean;\n}\n\nexport const menuCategories = [\n  \"Hot Drinks\",\n  \"Cold Drinks\", \n  \"Snacks\",\n  \"Bakery\",\n  \"Traditional Nepali\",\n  \"Breakfast\"\n];\n\nexport const menuItems: MenuItem[] = [\n  // Hot Drinks\n  {\n    id: \"hd1\",\n    name: \"Himalayan Coffee\",\n    description: \"Our signature blend from Ilam hills, rich and aromatic with notes of chocolate and nuts\",\n    price: 180,\n    image: \"https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=400&h=300&fit=crop\",\n    category: \"Hot Drinks\",\n    isPopular: true\n  },\n  {\n    id: \"hd2\",\n    name: \"Masala Chai\",\n    description: \"Traditional spiced tea with cardamom, cinnamon, and ginger, served with milk\",\n    price: 120,\n    image: \"https://images.unsplash.com/photo-1571934811356-5cc061b6821f?w=400&h=300&fit=crop\",\n    category: \"Hot Drinks\",\n    isPopular: true,\n    isVegetarian: true\n  },\n  {\n    id: \"hd3\",\n    name: \"Butter Tea (Po Cha)\",\n    description: \"Traditional Tibetan butter tea, perfect for cold Biratnagar mornings\",\n    price: 150,\n    image: \"https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=300&fit=crop\",\n    category: \"Hot Drinks\",\n    isVegetarian: true\n  },\n  {\n    id: \"hd4\",\n    name: \"Cappuccino\",\n    description: \"Classic Italian coffee with steamed milk foam and a sprinkle of cocoa\",\n    price: 200,\n    image: \"https://images.unsplash.com/photo-1572442388796-11668a67e53d?w=400&h=300&fit=crop\",\n    category: \"Hot Drinks\",\n    isVegetarian: true\n  },\n  {\n    id: \"hd5\",\n    name: \"Hot Chocolate\",\n    description: \"Rich and creamy hot chocolate topped with whipped cream\",\n    price: 220,\n    image: \"https://images.unsplash.com/photo-1542990253-0d0f5be5f0ed?w=400&h=300&fit=crop\",\n    category: \"Hot Drinks\",\n    isVegetarian: true\n  },\n\n  // Cold Drinks\n  {\n    id: \"cd1\",\n    name: \"Iced Himalayan Coffee\",\n    description: \"Our signature coffee served cold with ice and a touch of condensed milk\",\n    price: 200,\n    image: \"https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=400&h=300&fit=crop\",\n    category: \"Cold Drinks\",\n    isPopular: true\n  },\n  {\n    id: \"cd2\",\n    name: \"Mango Lassi\",\n    description: \"Creamy yogurt drink blended with fresh mango and cardamom\",\n    price: 180,\n    image: \"https://images.unsplash.com/photo-1553909489-cd47e0ef937f?w=400&h=300&fit=crop\",\n    category: \"Cold Drinks\",\n    isVegetarian: true\n  },\n  {\n    id: \"cd3\",\n    name: \"Fresh Lime Soda\",\n    description: \"Refreshing lime juice with soda water and mint leaves\",\n    price: 120,\n    image: \"https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=400&h=300&fit=crop\",\n    category: \"Cold Drinks\",\n    isVegan: true\n  },\n  {\n    id: \"cd4\",\n    name: \"Iced Chai Latte\",\n    description: \"Spiced chai served cold with milk and ice\",\n    price: 160,\n    image: \"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop\",\n    category: \"Cold Drinks\",\n    isVegetarian: true\n  },\n\n  // Snacks\n  {\n    id: \"sn1\",\n    name: \"Chicken Momo\",\n    description: \"Traditional Nepali dumplings filled with seasoned chicken, served with spicy sauce\",\n    price: 280,\n    image: \"https://images.unsplash.com/photo-1496116218417-1a781b1c416c?w=400&h=300&fit=crop\",\n    category: \"Snacks\",\n    isPopular: true\n  },\n  {\n    id: \"sn2\",\n    name: \"Vegetable Momo\",\n    description: \"Steamed dumplings filled with fresh vegetables and herbs\",\n    price: 240,\n    image: \"https://images.unsplash.com/photo-1563379091339-03246963d96a?w=400&h=300&fit=crop\",\n    category: \"Snacks\",\n    isVegetarian: true,\n    isPopular: true\n  },\n  {\n    id: \"sn3\",\n    name: \"Samosa Chat\",\n    description: \"Crispy samosas topped with yogurt, chutneys, and spices\",\n    price: 160,\n    image: \"https://images.unsplash.com/photo-1601050690597-df0568f70950?w=400&h=300&fit=crop\",\n    category: \"Snacks\",\n    isVegetarian: true\n  },\n  {\n    id: \"sn4\",\n    name: \"French Fries\",\n    description: \"Golden crispy fries served with ketchup and mayo\",\n    price: 180,\n    image: \"https://images.unsplash.com/photo-1573080496219-bb080dd4f877?w=400&h=300&fit=crop\",\n    category: \"Snacks\",\n    isVegetarian: true\n  },\n\n  // Bakery\n  {\n    id: \"bk1\",\n    name: \"Chocolate Croissant\",\n    description: \"Buttery croissant filled with rich dark chocolate\",\n    price: 150,\n    image: \"https://images.unsplash.com/photo-1555507036-ab794f4afe5e?w=400&h=300&fit=crop\",\n    category: \"Bakery\",\n    isVegetarian: true\n  },\n  {\n    id: \"bk2\",\n    name: \"Banana Bread\",\n    description: \"Homemade banana bread with walnuts, perfect with coffee\",\n    price: 120,\n    image: \"https://images.unsplash.com/photo-1586985289688-ca3cf47d3e6e?w=400&h=300&fit=crop\",\n    category: \"Bakery\",\n    isVegetarian: true,\n    isPopular: true\n  },\n  {\n    id: \"bk3\",\n    name: \"Blueberry Muffin\",\n    description: \"Fresh baked muffin loaded with juicy blueberries\",\n    price: 140,\n    image: \"https://images.unsplash.com/photo-1607958996333-41aef7caefaa?w=400&h=300&fit=crop\",\n    category: \"Bakery\",\n    isVegetarian: true\n  },\n\n  // Traditional Nepali\n  {\n    id: \"tn1\",\n    name: \"Dal Bhat Set\",\n    description: \"Traditional Nepali meal with lentil soup, rice, vegetables, and pickle\",\n    price: 350,\n    image: \"https://images.unsplash.com/photo-1585937421612-70a008356fbe?w=400&h=300&fit=crop\",\n    category: \"Traditional Nepali\",\n    isVegetarian: true,\n    isPopular: true\n  },\n  {\n    id: \"tn2\",\n    name: \"Sel Roti\",\n    description: \"Traditional ring-shaped rice bread, crispy outside and soft inside\",\n    price: 80,\n    image: \"https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=300&fit=crop\",\n    category: \"Traditional Nepali\",\n    isVegetarian: true\n  },\n\n  // Breakfast\n  {\n    id: \"bf1\",\n    name: \"English Breakfast\",\n    description: \"Eggs, toast, baked beans, and hash browns with coffee\",\n    price: 420,\n    image: \"https://images.unsplash.com/photo-1525351484163-7529414344d8?w=400&h=300&fit=crop\",\n    category: \"Breakfast\",\n    isVegetarian: true\n  },\n  {\n    id: \"bf2\",\n    name: \"Pancakes\",\n    description: \"Fluffy pancakes served with maple syrup and butter\",\n    price: 280,\n    image: \"https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400&h=300&fit=crop\",\n    category: \"Breakfast\",\n    isVegetarian: true\n  }\n];\n\nexport const popularItems = menuItems.filter(item => item.isPopular);\n\nexport const getItemsByCategory = (category: string) => {\n  return menuItems.filter(item => item.category === category);\n};\n"], "names": [], "mappings": ";;;;;;AAYO,MAAM,iBAAiB;IAC5B;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,YAAwB;IACnC,aAAa;IACb;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,WAAW;QACX,cAAc;IAChB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,cAAc;IAChB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,cAAc;IAChB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,cAAc;IAChB;IAEA,cAAc;IACd;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,cAAc;IAChB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,cAAc;IAChB;IAEA,SAAS;IACT;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,cAAc;QACd,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,cAAc;IAChB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,cAAc;IAChB;IAEA,SAAS;IACT;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,cAAc;IAChB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,cAAc;QACd,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,cAAc;IAChB;IAEA,qBAAqB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,cAAc;QACd,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,cAAc;IAChB;IAEA,YAAY;IACZ;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,cAAc;IAChB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,cAAc;IAChB;CACD;AAEM,MAAM,eAAe,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS;AAE5D,MAAM,qBAAqB,CAAC;IACjC,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;AACpD", "debugId": null}}, {"offset": {"line": 1384, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/menu-highlights.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Star, Leaf } from \"lucide-react\";\nimport { popularItems } from \"@/data/menu\";\nimport Link from \"next/link\";\nimport Image from \"next/image\";\n\nexport function MenuHighlights() {\n  // Get first 6 popular items for highlights\n  const highlightItems = popularItems.slice(0, 6);\n\n  return (\n    <section className=\"py-20 bg-cafe-cream\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6\">\n            <PERSON>u <PERSON>\n          </h2>\n          <p className=\"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed\">\n            Discover our most beloved dishes and drinks, crafted with love and the finest ingredients from Nepal and beyond.\n          </p>\n        </motion.div>\n\n        {/* Menu Items Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\">\n          {highlightItems.map((item, index) => (\n            <motion.div\n              key={item.id}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n            >\n              <Card className=\"h-full overflow-hidden border-cafe-beige hover:shadow-cafe-lg transition-all duration-300 group\">\n                {/* Image */}\n                <div className=\"relative aspect-[4/3] overflow-hidden\">\n                  <Image\n                    src={item.image}\n                    alt={item.name}\n                    width={400}\n                    height={300}\n                    className=\"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                  />\n                  <div className=\"absolute top-4 left-4 flex gap-2\">\n                    {item.isPopular && (\n                      <Badge className=\"bg-cafe-gold text-cafe-dark-brown font-semibold\">\n                        <Star className=\"h-3 w-3 mr-1\" />\n                        Popular\n                      </Badge>\n                    )}\n                    {item.isVegetarian && (\n                      <Badge variant=\"secondary\" className=\"bg-cafe-green text-white\">\n                        <Leaf className=\"h-3 w-3 mr-1\" />\n                        Veg\n                      </Badge>\n                    )}\n                  </div>\n                </div>\n\n                <CardContent className=\"p-6\">\n                  <div className=\"space-y-3\">\n                    {/* Name and Price */}\n                    <div className=\"flex justify-between items-start\">\n                      <h3 className=\"font-serif text-xl font-semibold text-cafe-dark-brown\">\n                        {item.name}\n                      </h3>\n                      <span className=\"text-lg font-bold text-cafe-brown\">\n                        Rs. {item.price}\n                      </span>\n                    </div>\n\n                    {/* Description */}\n                    <p className=\"text-cafe-brown leading-relaxed text-sm\">\n                      {item.description}\n                    </p>\n\n                    {/* Category */}\n                    <div className=\"pt-2\">\n                      <Badge variant=\"outline\" className=\"border-cafe-beige text-cafe-brown\">\n                        {item.category}\n                      </Badge>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* CTA Button */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.3 }}\n          viewport={{ once: true }}\n          className=\"text-center\"\n        >\n          <Link href=\"/menu\">\n            <Button \n              size=\"lg\" \n              className=\"bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream px-8 py-3 text-lg font-semibold shadow-cafe-lg\"\n            >\n              View Full Menu\n            </Button>\n          </Link>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWO,SAAS;IACd,2CAA2C;IAC3C,MAAM,iBAAiB,mHAAA,CAAA,eAAY,CAAC,KAAK,CAAC,GAAG;IAE7C,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAsE;;;;;;sCAGpF,8OAAC;4BAAE,WAAU;sCAA4D;;;;;;;;;;;;8BAM3E,8OAAC;oBAAI,WAAU;8BACZ,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDAEd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,KAAK,KAAK;gDACf,KAAK,KAAK,IAAI;gDACd,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;;oDACZ,KAAK,SAAS,kBACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAU;;0EACf,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;oDAIpC,KAAK,YAAY,kBAChB,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;;0EACnC,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;kDAOzC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,KAAK,IAAI;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;;gEAAoC;gEAC7C,KAAK,KAAK;;;;;;;;;;;;;8DAKnB,8OAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;8DAInB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAChC,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BApDnB,KAAK,EAAE;;;;;;;;;;8BA+DlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 1677, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/data/packages.ts"], "sourcesContent": ["export interface Package {\n  id: string;\n  name: string;\n  description: string;\n  originalPrice: number;\n  discountedPrice: number;\n  savings: number;\n  items: string[];\n  validUntil: string;\n  isPopular?: boolean;\n  image: string;\n}\n\nexport const packages: Package[] = [\n  {\n    id: \"p1\",\n    name: \"Morning Bliss Combo\",\n    description: \"Perfect start to your day with our signature coffee and fresh pastry\",\n    originalPrice: 350,\n    discountedPrice: 280,\n    savings: 70,\n    items: [\n      \"Himalayan Coffee (Regular)\",\n      \"Choice of Fresh Pastry\",\n      \"Free WiFi Access\"\n    ],\n    validUntil: \"2024-12-31\",\n    isPopular: true,\n    image: \"https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=400&h=300&fit=crop\"\n  },\n  {\n    id: \"p2\",\n    name: \"Study Buddy Package\",\n    description: \"Ideal for students - unlimited coffee refills and snacks for productive study sessions\",\n    originalPrice: 600,\n    discountedPrice: 450,\n    savings: 150,\n    items: [\n      \"Unlimited Coffee Refills (4 hours)\",\n      \"Choice of Snack\",\n      \"Reserved Study Table\",\n      \"Free WiFi & Power Outlet\"\n    ],\n    validUntil: \"2024-12-31\",\n    isPopular: true,\n    image: \"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=300&fit=crop\"\n  },\n  {\n    id: \"p3\",\n    name: \"Momo Mania\",\n    description: \"For momo lovers - try both chicken and vegetable momos with drinks\",\n    originalPrice: 640,\n    discountedPrice: 520,\n    savings: 120,\n    items: [\n      \"Chicken Momo (Half Plate)\",\n      \"Vegetable Momo (Half Plate)\",\n      \"2 Masala Chai\",\n      \"Complimentary Pickle\"\n    ],\n    validUntil: \"2024-12-31\",\n    image: \"https://images.unsplash.com/photo-1496116218417-1a781b1c416c?w=400&h=300&fit=crop\"\n  },\n  {\n    id: \"p4\",\n    name: \"Date Night Special\",\n    description: \"Romantic evening package for couples with premium coffee and desserts\",\n    originalPrice: 800,\n    discountedPrice: 650,\n    savings: 150,\n    items: [\n      \"2 Premium Coffee (Cappuccino/Latte)\",\n      \"Chocolate Croissant to Share\",\n      \"Reserved Corner Table\",\n      \"Complimentary Dessert\"\n    ],\n    validUntil: \"2024-12-31\",\n    image: \"https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop\"\n  },\n  {\n    id: \"p5\",\n    name: \"Family Feast\",\n    description: \"Perfect for family gatherings with traditional Nepali meal and drinks for 4\",\n    originalPrice: 1400,\n    discountedPrice: 1100,\n    savings: 300,\n    items: [\n      \"2 Dal Bhat Sets\",\n      \"1 Chicken Momo\",\n      \"1 Vegetable Momo\",\n      \"4 Drinks (Tea/Coffee/Lassi)\",\n      \"Family Table Reservation\"\n    ],\n    validUntil: \"2024-12-31\",\n    isPopular: true,\n    image: \"https://images.unsplash.com/photo-1543007630-9710e4a00a20?w=400&h=300&fit=crop\"\n  },\n  {\n    id: \"p6\",\n    name: \"Business Meeting Package\",\n    description: \"Professional setting with premium coffee and light snacks for your meetings\",\n    originalPrice: 1200,\n    discountedPrice: 950,\n    savings: 250,\n    items: [\n      \"Reserved Meeting Table (2 hours)\",\n      \"Premium Coffee for 4 people\",\n      \"Assorted Pastries\",\n      \"Notepad & Pen\",\n      \"Free WiFi & Presentation Setup\"\n    ],\n    validUntil: \"2024-12-31\",\n    image: \"/images/packages/business-meeting.jpg\"\n  },\n  {\n    id: \"p7\",\n    name: \"Weekend Brunch\",\n    description: \"Leisurely weekend brunch with hearty breakfast and unlimited coffee\",\n    originalPrice: 750,\n    discountedPrice: 600,\n    savings: 150,\n    items: [\n      \"English Breakfast OR Pancakes\",\n      \"Unlimited Coffee (2 hours)\",\n      \"Fresh Fruit Juice\",\n      \"Weekend Newspaper\",\n      \"Relaxed Atmosphere\"\n    ],\n    validUntil: \"2024-12-31\",\n    image: \"/images/packages/weekend-brunch.jpg\"\n  },\n  {\n    id: \"p8\",\n    name: \"Cultural Experience\",\n    description: \"Taste authentic Nepal with traditional foods and drinks\",\n    originalPrice: 550,\n    discountedPrice: 450,\n    savings: 100,\n    items: [\n      \"Butter Tea (Po Cha)\",\n      \"Sel Roti (2 pieces)\",\n      \"Traditional Pickle\",\n      \"Cultural Story Session\",\n      \"Photo with Traditional Dress\"\n    ],\n    validUntil: \"2024-12-31\",\n    image: \"/images/packages/cultural-experience.jpg\"\n  }\n];\n\nexport const popularPackages = packages.filter(pkg => pkg.isPopular);\n\nexport const getPackageById = (id: string) => {\n  return packages.find(pkg => pkg.id === id);\n};\n\nexport const calculateSavings = (originalPrice: number, discountedPrice: number) => {\n  return originalPrice - discountedPrice;\n};\n\nexport const calculateDiscountPercentage = (originalPrice: number, discountedPrice: number) => {\n  return Math.round(((originalPrice - discountedPrice) / originalPrice) * 100);\n};\n"], "names": [], "mappings": ";;;;;;;AAaO,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,iBAAiB;QACjB,SAAS;QACT,OAAO;YACL;YACA;YACA;SACD;QACD,YAAY;QACZ,WAAW;QACX,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,iBAAiB;QACjB,SAAS;QACT,OAAO;YACL;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,WAAW;QACX,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,iBAAiB;QACjB,SAAS;QACT,OAAO;YACL;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,iBAAiB;QACjB,SAAS;QACT,OAAO;YACL;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,iBAAiB;QACjB,SAAS;QACT,OAAO;YACL;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,WAAW;QACX,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,iBAAiB;QACjB,SAAS;QACT,OAAO;YACL;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,iBAAiB;QACjB,SAAS;QACT,OAAO;YACL;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,iBAAiB;QACjB,SAAS;QACT,OAAO;YACL;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;IACT;CACD;AAEM,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,MAAO,IAAI,SAAS;AAE5D,MAAM,iBAAiB,CAAC;IAC7B,OAAO,SAAS,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;AACzC;AAEO,MAAM,mBAAmB,CAAC,eAAuB;IACtD,OAAO,gBAAgB;AACzB;AAEO,MAAM,8BAA8B,CAAC,eAAuB;IACjE,OAAO,KAAK,KAAK,CAAC,AAAC,CAAC,gBAAgB,eAAe,IAAI,gBAAiB;AAC1E", "debugId": null}}, {"offset": {"line": 1834, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/packages-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON>eader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { Check, Star, Gift } from \"lucide-react\";\nimport { popularPackages, calculateDiscountPercentage } from \"@/data/packages\";\nimport Image from \"next/image\";\n\nexport function PackagesSection() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6\">\n            Special Packages\n          </h2>\n          <p className=\"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed\">\n            Enjoy great savings with our carefully curated packages, perfect for every occasion and craving.\n          </p>\n        </motion.div>\n\n        {/* Packages Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {popularPackages.map((pkg, index) => {\n            const discountPercentage = calculateDiscountPercentage(pkg.originalPrice, pkg.discountedPrice);\n            \n            return (\n              <motion.div\n                key={pkg.id}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n              >\n                <Card className=\"h-full border-cafe-beige hover:shadow-cafe-lg transition-all duration-300 relative overflow-hidden\">\n                  {/* Popular Badge */}\n                  {pkg.isPopular && (\n                    <div className=\"absolute top-4 right-4 z-10\">\n                      <Badge className=\"bg-cafe-gold text-cafe-dark-brown font-semibold\">\n                        <Star className=\"h-3 w-3 mr-1\" />\n                        Popular\n                      </Badge>\n                    </div>\n                  )}\n\n                  {/* Discount Badge */}\n                  <div className=\"absolute top-4 left-4 z-10\">\n                    <Badge className=\"bg-cafe-green text-white font-semibold\">\n                      {discountPercentage}% OFF\n                    </Badge>\n                  </div>\n\n                  {/* Package Image */}\n                  <div className=\"aspect-[4/3] overflow-hidden\">\n                    <Image\n                      src={pkg.image}\n                      alt={pkg.name}\n                      width={400}\n                      height={300}\n                      className=\"w-full h-full object-cover\"\n                    />\n                  </div>\n\n                  <CardHeader className=\"pb-4\">\n                    <CardTitle className=\"font-serif text-xl text-cafe-dark-brown flex items-center gap-2\">\n                      <Gift className=\"h-5 w-5 text-cafe-brown\" />\n                      {pkg.name}\n                    </CardTitle>\n                    <p className=\"text-cafe-brown text-sm leading-relaxed\">\n                      {pkg.description}\n                    </p>\n                  </CardHeader>\n\n                  <CardContent className=\"space-y-4\">\n                    {/* Pricing */}\n                    <div className=\"flex items-center gap-3\">\n                      <span className=\"text-2xl font-bold text-cafe-dark-brown\">\n                        Rs. {pkg.discountedPrice}\n                      </span>\n                      <span className=\"text-lg text-cafe-brown line-through\">\n                        Rs. {pkg.originalPrice}\n                      </span>\n                      <Badge variant=\"secondary\" className=\"bg-cafe-beige text-cafe-brown\">\n                        Save Rs. {pkg.savings}\n                      </Badge>\n                    </div>\n\n                    {/* Items Included */}\n                    <div className=\"space-y-2\">\n                      <h4 className=\"font-semibold text-cafe-dark-brown text-sm\">\n                        What&apos;s Included:\n                      </h4>\n                      <ul className=\"space-y-1\">\n                        {pkg.items.map((item, itemIndex) => (\n                          <li key={itemIndex} className=\"flex items-start gap-2 text-sm text-cafe-brown\">\n                            <Check className=\"h-4 w-4 text-cafe-green mt-0.5 flex-shrink-0\" />\n                            <span>{item}</span>\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n\n                    {/* Valid Until */}\n                    <div className=\"text-xs text-cafe-brown\">\n                      Valid until: {new Date(pkg.validUntil).toLocaleDateString()}\n                    </div>\n\n                    {/* CTA Button */}\n                    <Button \n                      className=\"w-full bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream font-semibold\"\n                      size=\"sm\"\n                    >\n                      Order Now\n                    </Button>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            );\n          })}\n        </div>\n\n        {/* View All Packages Button */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.3 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-12\"\n        >\n          <Button \n            variant=\"outline\"\n            size=\"lg\" \n            className=\"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream px-8 py-3 text-lg font-semibold\"\n          >\n            View All Packages\n          </Button>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AARA;;;;;;;;;AAUO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAsE;;;;;;sCAGpF,8OAAC;4BAAE,WAAU;sCAA4D;;;;;;;;;;;;8BAM3E,8OAAC;oBAAI,WAAU;8BACZ,uHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,CAAC,KAAK;wBACzB,MAAM,qBAAqB,CAAA,GAAA,uHAAA,CAAA,8BAA2B,AAAD,EAAE,IAAI,aAAa,EAAE,IAAI,eAAe;wBAE7F,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;oCAEb,IAAI,SAAS,kBACZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAOvC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;;gDACd;gDAAmB;;;;;;;;;;;;kDAKxB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,IAAI,KAAK;4CACd,KAAK,IAAI,IAAI;4CACb,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;;;;;;kDAId,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACf,IAAI,IAAI;;;;;;;0DAEX,8OAAC;gDAAE,WAAU;0DACV,IAAI,WAAW;;;;;;;;;;;;kDAIpB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;4DAA0C;4DACnD,IAAI,eAAe;;;;;;;kEAE1B,8OAAC;wDAAK,WAAU;;4DAAuC;4DAChD,IAAI,aAAa;;;;;;;kEAExB,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;;4DAAgC;4DACzD,IAAI,OAAO;;;;;;;;;;;;;0DAKzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAG3D,8OAAC;wDAAG,WAAU;kEACX,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACpB,8OAAC;gEAAmB,WAAU;;kFAC5B,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;kFAAM;;;;;;;+DAFA;;;;;;;;;;;;;;;;0DASf,8OAAC;gDAAI,WAAU;;oDAA0B;oDACzB,IAAI,KAAK,IAAI,UAAU,EAAE,kBAAkB;;;;;;;0DAI3D,8OAAC,kIAAA,CAAA,SAAM;gDACL,WAAU;gDACV,MAAK;0DACN;;;;;;;;;;;;;;;;;;2BAnFA,IAAI,EAAE;;;;;oBA0FjB;;;;;;8BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 2202, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/data/gallery.ts"], "sourcesContent": ["export interface GalleryImage {\n  id: string;\n  src: string;\n  alt: string;\n  title: string;\n  category: 'interior' | 'food' | 'drinks' | 'people' | 'exterior';\n  description?: string;\n}\n\nexport const galleryImages: GalleryImage[] = [\n  // Interior\n  {\n    id: \"g1\",\n    src: \"https://images.unsplash.com/photo-1554118811-1e0d58224f24?w=600&h=400&fit=crop\",\n    alt: \"Main seating area with cozy wooden furniture\",\n    title: \"Cozy Main Seating Area\",\n    category: \"interior\",\n    description: \"Our warm and inviting main seating area with handcrafted wooden furniture\"\n  },\n  {\n    id: \"g2\",\n    src: \"https://images.unsplash.com/photo-1559925393-8be0ec4767c8?w=600&h=400&fit=crop\",\n    alt: \"Corner reading nook with books and plants\",\n    title: \"Reading Corner\",\n    category: \"interior\",\n    description: \"Perfect corner for book lovers with natural lighting and plants\"\n  },\n  {\n    id: \"g3\",\n    src: \"https://images.unsplash.com/photo-1442975631115-c4f7b05b8a2c?w=600&h=400&fit=crop\",\n    alt: \"Coffee counter with barista equipment\",\n    title: \"Coffee Counter\",\n    category: \"interior\",\n    description: \"Our professional coffee counter where magic happens\"\n  },\n  {\n    id: \"g4\",\n    src: \"https://images.unsplash.com/photo-1521017432531-fbd92d768814?w=600&h=400&fit=crop\",\n    alt: \"Upstairs seating area with mountain views\",\n    title: \"Upstairs Seating\",\n    category: \"interior\",\n    description: \"Second floor seating with beautiful mountain views\"\n  },\n\n  // Food\n  {\n    id: \"g5\",\n    src: \"https://images.unsplash.com/photo-1496116218417-1a781b1c416c?w=600&h=400&fit=crop\",\n    alt: \"Fresh steamed momos with sauce\",\n    title: \"Traditional Momos\",\n    category: \"food\",\n    description: \"Our signature momos, handmade fresh daily\"\n  },\n  {\n    id: \"g6\",\n    src: \"https://images.unsplash.com/photo-1585937421612-70a008356fbe?w=600&h=400&fit=crop\",\n    alt: \"Traditional dal bhat set meal\",\n    title: \"Dal Bhat Set\",\n    category: \"food\",\n    description: \"Authentic Nepali dal bhat with fresh vegetables\"\n  },\n  {\n    id: \"g7\",\n    src: \"https://images.unsplash.com/photo-1555507036-ab794f4afe5e?w=600&h=400&fit=crop\",\n    alt: \"Fresh baked pastries and bread\",\n    title: \"Fresh Pastries\",\n    category: \"food\",\n    description: \"Daily baked pastries and bread made in-house\"\n  },\n  {\n    id: \"g8\",\n    src: \"https://images.unsplash.com/photo-1525351484163-7529414344d8?w=600&h=400&fit=crop\",\n    alt: \"English breakfast plate\",\n    title: \"Hearty Breakfast\",\n    category: \"food\",\n    description: \"Start your day with our hearty breakfast options\"\n  },\n\n  // Drinks\n  {\n    id: \"g9\",\n    src: \"https://images.unsplash.com/photo-1514432324607-a09d9b4aefdd?w=600&h=400&fit=crop\",\n    alt: \"Latte with beautiful coffee art\",\n    title: \"Coffee Art\",\n    category: \"drinks\",\n    description: \"Our baristas create beautiful latte art with every cup\"\n  },\n  {\n    id: \"g10\",\n    src: \"https://images.unsplash.com/photo-1571934811356-5cc061b6821f?w=600&h=400&fit=crop\",\n    alt: \"Traditional masala chai in glass\",\n    title: \"Masala Chai\",\n    category: \"drinks\",\n    description: \"Traditional spiced chai served the authentic way\"\n  },\n  {\n    id: \"g11\",\n    src: \"https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=600&h=400&fit=crop\",\n    alt: \"Iced coffee with condensed milk\",\n    title: \"Iced Himalayan Coffee\",\n    category: \"drinks\",\n    description: \"Our signature coffee served cold and refreshing\"\n  },\n  {\n    id: \"g12\",\n    src: \"https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=600&h=400&fit=crop\",\n    alt: \"Various drinks on wooden table\",\n    title: \"Drink Selection\",\n    category: \"drinks\",\n    description: \"Wide variety of hot and cold beverages\"\n  },\n\n  // People\n  {\n    id: \"g13\",\n    src: \"https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop\",\n    alt: \"Friends enjoying coffee together\",\n    title: \"Friends Gathering\",\n    category: \"people\",\n    description: \"Friends enjoying quality time over coffee\"\n  },\n  {\n    id: \"g14\",\n    src: \"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=400&fit=crop\",\n    alt: \"Students studying with laptops\",\n    title: \"Study Session\",\n    category: \"people\",\n    description: \"Students finding the perfect study environment\"\n  },\n  {\n    id: \"g15\",\n    src: \"https://images.unsplash.com/photo-1543007630-9710e4a00a20?w=600&h=400&fit=crop\",\n    alt: \"Family enjoying meal together\",\n    title: \"Family Time\",\n    category: \"people\",\n    description: \"Families creating memories over delicious food\"\n  },\n  {\n    id: \"g16\",\n    src: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=400&fit=crop\",\n    alt: \"Barista preparing coffee\",\n    title: \"Our Barista\",\n    category: \"people\",\n    description: \"Skilled baristas crafting the perfect cup\"\n  },\n\n  // Exterior\n  {\n    id: \"g17\",\n    src: \"https://images.unsplash.com/photo-1554118811-1e0d58224f24?w=600&h=400&fit=crop\",\n    alt: \"Café exterior with signage\",\n    title: \"Café Exterior\",\n    category: \"exterior\",\n    description: \"Welcome to Himalayan Brew Café on Main Road\"\n  },\n  {\n    id: \"g18\",\n    src: \"https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=600&h=400&fit=crop\",\n    alt: \"Outdoor terrace seating\",\n    title: \"Outdoor Terrace\",\n    category: \"exterior\",\n    description: \"Enjoy your coffee in our outdoor terrace area\"\n  },\n  {\n    id: \"g19\",\n    src: \"https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?w=600&h=400&fit=crop\",\n    alt: \"Café in the evening with warm lighting\",\n    title: \"Evening Ambiance\",\n    category: \"exterior\",\n    description: \"Cozy evening atmosphere with warm lighting\"\n  },\n  {\n    id: \"g20\",\n    src: \"https://images.unsplash.com/photo-1559925393-8be0ec4767c8?w=600&h=400&fit=crop\",\n    alt: \"Street view of the café location\",\n    title: \"Street View\",\n    category: \"exterior\",\n    description: \"Located in the heart of Biratnagar's main road\"\n  }\n];\n\nexport const getImagesByCategory = (category: GalleryImage['category']) => {\n  return galleryImages.filter(image => image.category === category);\n};\n\nexport const featuredImages = galleryImages.slice(0, 8);\n\nexport const galleryCategories = [\n  { key: 'all', label: 'All Photos' },\n  { key: 'interior', label: 'Interior' },\n  { key: 'food', label: 'Food' },\n  { key: 'drinks', label: 'Drinks' },\n  { key: 'people', label: 'People' },\n  { key: 'exterior', label: 'Exterior' }\n] as const;\n"], "names": [], "mappings": ";;;;;;AASO,MAAM,gBAAgC;IAC3C,WAAW;IACX;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;IACf;IAEA,OAAO;IACP;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;IACf;IAEA,SAAS;IACT;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;IACf;IAEA,SAAS;IACT;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;IACf;IAEA,WAAW;IACX;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;IACf;CACD;AAEM,MAAM,sBAAsB,CAAC;IAClC,OAAO,cAAc,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;AAC1D;AAEO,MAAM,iBAAiB,cAAc,KAAK,CAAC,GAAG;AAE9C,MAAM,oBAAoB;IAC/B;QAAE,KAAK;QAAO,OAAO;IAAa;IAClC;QAAE,KAAK;QAAY,OAAO;IAAW;IACrC;QAAE,KAAK;QAAQ,OAAO;IAAO;IAC7B;QAAE,KAAK;QAAU,OAAO;IAAS;IACjC;QAAE,KAAK;QAAU,OAAO;IAAS;IACjC;QAAE,KAAK;QAAY,OAAO;IAAW;CACtC", "debugId": null}}, {"offset": {"line": 2581, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/gallery-preview.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Dialog, DialogContent, DialogTrigger } from \"@/components/ui/dialog\";\nimport { Camera } from \"lucide-react\";\nimport { featuredImages } from \"@/data/gallery\";\nimport Link from \"next/link\";\nimport Image from \"next/image\";\n\nexport function GalleryPreview() {\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6\">\n            Gallery\n          </h2>\n          <p className=\"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed\">\n            Take a visual journey through our cozy café, delicious food, and the warm moments shared by our community.\n          </p>\n        </motion.div>\n\n        {/* Gallery Grid */}\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-12\">\n          {featuredImages.map((image, index) => (\n            <motion.div\n              key={image.id}\n              initial={{ opacity: 0, scale: 0.9 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.5, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className={`relative group cursor-pointer overflow-hidden rounded-lg ${\n                index === 0 ? 'col-span-2 row-span-2' : \n                index === 3 ? 'col-span-2' : ''\n              }`}\n            >\n              <Dialog>\n                <DialogTrigger asChild>\n                  <div className=\"relative aspect-square overflow-hidden rounded-lg\">\n                    <Image\n                      src={image.src}\n                      alt={image.alt}\n                      width={400}\n                      height={400}\n                      className=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-300\"\n                    />\n                    \n                    {/* Overlay */}\n                    <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-colors duration-300 flex items-center justify-center\">\n                      <div className=\"opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                        <div className=\"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center\">\n                          <Camera className=\"h-6 w-6 text-white\" />\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Category Badge */}\n                    <div className=\"absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                      <span className=\"bg-cafe-brown/80 text-cafe-cream text-xs px-2 py-1 rounded-full backdrop-blur-sm\">\n                        {image.category}\n                      </span>\n                    </div>\n                  </div>\n                </DialogTrigger>\n\n                <DialogContent className=\"max-w-4xl w-full p-0 border-0\">\n                  <div className=\"relative\">\n                    <Image\n                      src={image.src}\n                      alt={image.alt}\n                      width={800}\n                      height={600}\n                      className=\"w-full h-auto max-h-[80vh] object-contain\"\n                    />\n                    <div className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-6\">\n                      <h3 className=\"text-white font-serif text-xl font-semibold mb-2\">\n                        {image.title}\n                      </h3>\n                      {image.description && (\n                        <p className=\"text-white/90 text-sm\">\n                          {image.description}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n                </DialogContent>\n              </Dialog>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* View All Button */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.3 }}\n          viewport={{ once: true }}\n          className=\"text-center\"\n        >\n          <Link href=\"/gallery\">\n            <Button \n              size=\"lg\" \n              className=\"bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream px-8 py-3 text-lg font-semibold shadow-cafe-lg\"\n            >\n              <Camera className=\"h-5 w-5 mr-2\" />\n              View Full Gallery\n            </Button>\n          </Link>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUO,SAAS;IAEd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAsE;;;;;;sCAGpF,8OAAC;4BAAE,WAAU;sCAA4D;;;;;;;;;;;;8BAM3E,8OAAC;oBAAI,WAAU;8BACZ,sHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,aAAa;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BACpC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAW,CAAC,yDAAyD,EACnE,UAAU,IAAI,0BACd,UAAU,IAAI,eAAe,IAC7B;sCAEF,cAAA,8OAAC,kIAAA,CAAA,SAAM;;kDACL,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,OAAO;kDACpB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,MAAM,GAAG;oDACd,KAAK,MAAM,GAAG;oDACd,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;8DAIZ,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;8DAMxB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEACb,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;kDAMvB,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,MAAM,GAAG;oDACd,KAAK,MAAM,GAAG;oDACd,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,MAAM,KAAK;;;;;;wDAEb,MAAM,WAAW,kBAChB,8OAAC;4DAAE,WAAU;sEACV,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAtDzB,MAAM,EAAE;;;;;;;;;;8BAkEnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,WAAU;;8CAEV,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}, {"offset": {"line": 2868, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/ui/carousel.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport useEmblaCarousel, {\n  type UseEmblaCarouselType,\n} from \"embla-carousel-react\"\nimport { ArrowLeft, ArrowRight } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\nimport { But<PERSON> } from \"@/components/ui/button\"\n\ntype CarouselApi = UseEmblaCarouselType[1]\ntype UseCarouselParameters = Parameters<typeof useEmblaCarousel>\ntype CarouselOptions = UseCarouselParameters[0]\ntype CarouselPlugin = UseCarouselParameters[1]\n\ntype CarouselProps = {\n  opts?: CarouselOptions\n  plugins?: CarouselPlugin\n  orientation?: \"horizontal\" | \"vertical\"\n  setApi?: (api: CarouselApi) => void\n}\n\ntype CarouselContextProps = {\n  carouselRef: ReturnType<typeof useEmblaCarousel>[0]\n  api: ReturnType<typeof useEmblaCarousel>[1]\n  scrollPrev: () => void\n  scrollNext: () => void\n  canScrollPrev: boolean\n  canScrollNext: boolean\n} & CarouselProps\n\nconst CarouselContext = React.createContext<CarouselContextProps | null>(null)\n\nfunction useCarousel() {\n  const context = React.useContext(CarouselContext)\n\n  if (!context) {\n    throw new Error(\"useCarousel must be used within a <Carousel />\")\n  }\n\n  return context\n}\n\nfunction Carousel({\n  orientation = \"horizontal\",\n  opts,\n  setApi,\n  plugins,\n  className,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & CarouselProps) {\n  const [carouselRef, api] = useEmblaCarousel(\n    {\n      ...opts,\n      axis: orientation === \"horizontal\" ? \"x\" : \"y\",\n    },\n    plugins\n  )\n  const [canScrollPrev, setCanScrollPrev] = React.useState(false)\n  const [canScrollNext, setCanScrollNext] = React.useState(false)\n\n  const onSelect = React.useCallback((api: CarouselApi) => {\n    if (!api) return\n    setCanScrollPrev(api.canScrollPrev())\n    setCanScrollNext(api.canScrollNext())\n  }, [])\n\n  const scrollPrev = React.useCallback(() => {\n    api?.scrollPrev()\n  }, [api])\n\n  const scrollNext = React.useCallback(() => {\n    api?.scrollNext()\n  }, [api])\n\n  const handleKeyDown = React.useCallback(\n    (event: React.KeyboardEvent<HTMLDivElement>) => {\n      if (event.key === \"ArrowLeft\") {\n        event.preventDefault()\n        scrollPrev()\n      } else if (event.key === \"ArrowRight\") {\n        event.preventDefault()\n        scrollNext()\n      }\n    },\n    [scrollPrev, scrollNext]\n  )\n\n  React.useEffect(() => {\n    if (!api || !setApi) return\n    setApi(api)\n  }, [api, setApi])\n\n  React.useEffect(() => {\n    if (!api) return\n    onSelect(api)\n    api.on(\"reInit\", onSelect)\n    api.on(\"select\", onSelect)\n\n    return () => {\n      api?.off(\"select\", onSelect)\n    }\n  }, [api, onSelect])\n\n  return (\n    <CarouselContext.Provider\n      value={{\n        carouselRef,\n        api: api,\n        opts,\n        orientation:\n          orientation || (opts?.axis === \"y\" ? \"vertical\" : \"horizontal\"),\n        scrollPrev,\n        scrollNext,\n        canScrollPrev,\n        canScrollNext,\n      }}\n    >\n      <div\n        onKeyDownCapture={handleKeyDown}\n        className={cn(\"relative\", className)}\n        role=\"region\"\n        aria-roledescription=\"carousel\"\n        data-slot=\"carousel\"\n        {...props}\n      >\n        {children}\n      </div>\n    </CarouselContext.Provider>\n  )\n}\n\nfunction CarouselContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  const { carouselRef, orientation } = useCarousel()\n\n  return (\n    <div\n      ref={carouselRef}\n      className=\"overflow-hidden\"\n      data-slot=\"carousel-content\"\n    >\n      <div\n        className={cn(\n          \"flex\",\n          orientation === \"horizontal\" ? \"-ml-4\" : \"-mt-4 flex-col\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction CarouselItem({ className, ...props }: React.ComponentProps<\"div\">) {\n  const { orientation } = useCarousel()\n\n  return (\n    <div\n      role=\"group\"\n      aria-roledescription=\"slide\"\n      data-slot=\"carousel-item\"\n      className={cn(\n        \"min-w-0 shrink-0 grow-0 basis-full\",\n        orientation === \"horizontal\" ? \"pl-4\" : \"pt-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CarouselPrevious({\n  className,\n  variant = \"outline\",\n  size = \"icon\",\n  ...props\n}: React.ComponentProps<typeof Button>) {\n  const { orientation, scrollPrev, canScrollPrev } = useCarousel()\n\n  return (\n    <Button\n      data-slot=\"carousel-previous\"\n      variant={variant}\n      size={size}\n      className={cn(\n        \"absolute size-8 rounded-full\",\n        orientation === \"horizontal\"\n          ? \"top-1/2 -left-12 -translate-y-1/2\"\n          : \"-top-12 left-1/2 -translate-x-1/2 rotate-90\",\n        className\n      )}\n      disabled={!canScrollPrev}\n      onClick={scrollPrev}\n      {...props}\n    >\n      <ArrowLeft />\n      <span className=\"sr-only\">Previous slide</span>\n    </Button>\n  )\n}\n\nfunction CarouselNext({\n  className,\n  variant = \"outline\",\n  size = \"icon\",\n  ...props\n}: React.ComponentProps<typeof Button>) {\n  const { orientation, scrollNext, canScrollNext } = useCarousel()\n\n  return (\n    <Button\n      data-slot=\"carousel-next\"\n      variant={variant}\n      size={size}\n      className={cn(\n        \"absolute size-8 rounded-full\",\n        orientation === \"horizontal\"\n          ? \"top-1/2 -right-12 -translate-y-1/2\"\n          : \"-bottom-12 left-1/2 -translate-x-1/2 rotate-90\",\n        className\n      )}\n      disabled={!canScrollNext}\n      onClick={scrollNext}\n      {...props}\n    >\n      <ArrowRight />\n      <span className=\"sr-only\">Next slide</span>\n    </Button>\n  )\n}\n\nexport {\n  type CarouselApi,\n  Carousel,\n  CarouselContent,\n  CarouselItem,\n  CarouselPrevious,\n  CarouselNext,\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AAGA;AAAA;AAEA;AACA;AATA;;;;;;;AAgCA,MAAM,gCAAkB,qMAAA,CAAA,gBAAmB,CAA8B;AAEzE,SAAS;IACP,MAAM,UAAU,qMAAA,CAAA,aAAgB,CAAC;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,SAAS,EAChB,cAAc,YAAY,EAC1B,IAAI,EACJ,MAAM,EACN,OAAO,EACP,SAAS,EACT,QAAQ,EACR,GAAG,OACyC;IAC5C,MAAM,CAAC,aAAa,IAAI,GAAG,CAAA,GAAA,sLAAA,CAAA,UAAgB,AAAD,EACxC;QACE,GAAG,IAAI;QACP,MAAM,gBAAgB,eAAe,MAAM;IAC7C,GACA;IAEF,MAAM,CAAC,eAAe,iBAAiB,GAAG,qMAAA,CAAA,WAAc,CAAC;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,qMAAA,CAAA,WAAc,CAAC;IAEzD,MAAM,WAAW,qMAAA,CAAA,cAAiB,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK;QACV,iBAAiB,IAAI,aAAa;QAClC,iBAAiB,IAAI,aAAa;IACpC,GAAG,EAAE;IAEL,MAAM,aAAa,qMAAA,CAAA,cAAiB,CAAC;QACnC,KAAK;IACP,GAAG;QAAC;KAAI;IAER,MAAM,aAAa,qMAAA,CAAA,cAAiB,CAAC;QACnC,KAAK;IACP,GAAG;QAAC;KAAI;IAER,MAAM,gBAAgB,qMAAA,CAAA,cAAiB,CACrC,CAAC;QACC,IAAI,MAAM,GAAG,KAAK,aAAa;YAC7B,MAAM,cAAc;YACpB;QACF,OAAO,IAAI,MAAM,GAAG,KAAK,cAAc;YACrC,MAAM,cAAc;YACpB;QACF;IACF,GACA;QAAC;QAAY;KAAW;IAG1B,qMAAA,CAAA,YAAe,CAAC;QACd,IAAI,CAAC,OAAO,CAAC,QAAQ;QACrB,OAAO;IACT,GAAG;QAAC;QAAK;KAAO;IAEhB,qMAAA,CAAA,YAAe,CAAC;QACd,IAAI,CAAC,KAAK;QACV,SAAS;QACT,IAAI,EAAE,CAAC,UAAU;QACjB,IAAI,EAAE,CAAC,UAAU;QAEjB,OAAO;YACL,KAAK,IAAI,UAAU;QACrB;IACF,GAAG;QAAC;QAAK;KAAS;IAElB,qBACE,8OAAC,gBAAgB,QAAQ;QACvB,OAAO;YACL;YACA,KAAK;YACL;YACA,aACE,eAAe,CAAC,MAAM,SAAS,MAAM,aAAa,YAAY;YAChE;YACA;YACA;YACA;QACF;kBAEA,cAAA,8OAAC;YACC,kBAAkB;YAClB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;YAC1B,MAAK;YACL,wBAAqB;YACrB,aAAU;YACT,GAAG,KAAK;sBAER;;;;;;;;;;;AAIT;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;IAErC,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,aAAU;kBAEV,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,QACA,gBAAgB,eAAe,UAAU,kBACzC;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,MAAM,EAAE,WAAW,EAAE,GAAG;IAExB,qBACE,8OAAC;QACC,MAAK;QACL,wBAAqB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sCACA,gBAAgB,eAAe,SAAS,QACxC;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,MAAM,EACb,GAAG,OACiC;IACpC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEnD,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,aAAU;QACV,SAAS;QACT,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gCACA,gBAAgB,eACZ,sCACA,+CACJ;QAEF,UAAU,CAAC;QACX,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,YAAS;;;;;0BACV,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,MAAM,EACb,GAAG,OACiC;IACpC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEnD,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,aAAU;QACV,SAAS;QACT,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gCACA,gBAAgB,eACZ,uCACA,kDACJ;QAEF,UAAU,CAAC;QACX,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC,kNAAA,CAAA,aAAU;;;;;0BACX,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 3081, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/data/testimonials.ts"], "sourcesContent": ["export interface Testimonial {\n  id: string;\n  name: string;\n  role: string;\n  content: string;\n  rating: number;\n  image: string;\n  date: string;\n}\n\nexport const testimonials: Testimonial[] = [\n  {\n    id: \"t1\",\n    name: \"<PERSON><PERSON>\",\n    role: \"Local Student\",\n    content: \"Himalayan Brew has become my second home! The coffee is amazing and the atmosphere is perfect for studying. The staff always remembers my order - iced chai latte with extra foam. It's the best café in Biratnagar!\",\n    rating: 5,\n    image: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face\",\n    date: \"2024-01-15\"\n  },\n  {\n    id: \"t2\",\n    name: \"<PERSON><PERSON>\",\n    role: \"Business Owner\",\n    content: \"I've been coming here for business meetings for over a year. The ambiance is professional yet cozy, and their Himalayan coffee is exceptional. My clients are always impressed with this place.\",\n    rating: 5,\n    image: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\n    date: \"2024-01-10\"\n  },\n  {\n    id: \"t3\",\n    name: \"<PERSON>\",\n    role: \"Tourist from Australia\",\n    content: \"Found this gem while exploring Biratnagar! The butter tea was an authentic experience and the momos were the best I had in Nepal. The staff was so welcoming and helped me understand local culture.\",\n    rating: 5,\n    image: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\",\n    date: \"2024-01-08\"\n  },\n  {\n    id: \"t4\",\n    name: \"Amit Rai\",\n    role: \"Software Engineer\",\n    content: \"Perfect spot for remote work! Fast WiFi, comfortable seating, and endless coffee refills. The banana bread is addictive. I practically live here during my work-from-café days.\",\n    rating: 5,\n    image: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\",\n    date: \"2024-01-05\"\n  },\n  {\n    id: \"t5\",\n    name: \"Sunita Devi\",\n    role: \"Teacher\",\n    content: \"Love bringing my family here on weekends. The kids enjoy the hot chocolate while we adults savor the masala chai. It's become our family tradition. Great place for all ages!\",\n    rating: 5,\n    image: \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face\",\n    date: \"2024-01-03\"\n  },\n  {\n    id: \"t6\",\n    name: \"David Chen\",\n    role: \"Travel Blogger\",\n    content: \"Himalayan Brew captures the essence of Nepali hospitality perfectly. The coffee quality rivals any international chain, but with authentic local charm. A must-visit in Biratnagar!\",\n    rating: 5,\n    image: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face\",\n    date: \"2023-12-28\"\n  },\n  {\n    id: \"t7\",\n    name: \"Kamala Gurung\",\n    role: \"Local Artist\",\n    content: \"This café has soul! I love how they support local artists by displaying our work. The creative atmosphere and excellent coffee make it my favorite spot for inspiration.\",\n    rating: 5,\n    image: \"https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face\",\n    date: \"2023-12-25\"\n  },\n  {\n    id: \"t8\",\n    name: \"Michael Roberts\",\n    role: \"NGO Worker\",\n    content: \"Been working in Nepal for 3 years and this is hands down the best café I've found. The dal bhat set is authentic and delicious. Feels like eating at a Nepali friend's home.\",\n    rating: 5,\n    image: \"https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face\",\n    date: \"2023-12-20\"\n  }\n];\n\nexport const featuredTestimonials = testimonials.slice(0, 3);\n\nexport const getTestimonialsByRating = (rating: number) => {\n  return testimonials.filter(testimonial => testimonial.rating === rating);\n};\n\nexport const averageRating = testimonials.reduce((sum, testimonial) => sum + testimonial.rating, 0) / testimonials.length;\n"], "names": [], "mappings": ";;;;;;AAUO,MAAM,eAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,OAAO;QACP,MAAM;IACR;CACD;AAEM,MAAM,uBAAuB,aAAa,KAAK,CAAC,GAAG;AAEnD,MAAM,0BAA0B,CAAC;IACtC,OAAO,aAAa,MAAM,CAAC,CAAA,cAAe,YAAY,MAAM,KAAK;AACnE;AAEO,MAAM,gBAAgB,aAAa,MAAM,CAAC,CAAC,KAAK,cAAgB,MAAM,YAAY,MAAM,EAAE,KAAK,aAAa,MAAM", "debugId": null}}, {"offset": {"line": 3170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/testimonials-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { \n  Carousel,\n  CarouselContent,\n  CarouselItem,\n  CarouselNext,\n  CarouselPrevious,\n} from \"@/components/ui/carousel\";\nimport { Star, Quote } from \"lucide-react\";\nimport { featuredTestimonials } from \"@/data/testimonials\";\nimport Link from \"next/link\";\nimport Image from \"next/image\";\n\nexport function TestimonialsSection() {\n  return (\n    <section className=\"py-20 bg-cafe-beige\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6\">\n            What Our Guests Say\n          </h2>\n          <p className=\"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed\">\n            Don&apos;t just take our word for it. Here&apos;s what our wonderful customers have to say about their experience at Himalayan Brew Café.\n          </p>\n        </motion.div>\n\n        {/* Testimonials Carousel */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          viewport={{ once: true }}\n          className=\"mb-12\"\n        >\n          <Carousel\n            opts={{\n              align: \"start\",\n              loop: true,\n            }}\n            className=\"w-full\"\n          >\n            <CarouselContent className=\"-ml-2 md:-ml-4\">\n              {featuredTestimonials.map((testimonial) => (\n                <CarouselItem key={testimonial.id} className=\"pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3\">\n                  <Card className=\"h-full border-cafe-sand hover:shadow-cafe-lg transition-shadow duration-300\">\n                    <CardContent className=\"p-6 space-y-4\">\n                      {/* Quote Icon */}\n                      <div className=\"flex justify-center\">\n                        <div className=\"w-12 h-12 bg-cafe-gold rounded-full flex items-center justify-center\">\n                          <Quote className=\"h-6 w-6 text-cafe-dark-brown\" />\n                        </div>\n                      </div>\n\n                      {/* Rating */}\n                      <div className=\"flex justify-center gap-1\">\n                        {[...Array(testimonial.rating)].map((_, i) => (\n                          <Star key={i} className=\"h-5 w-5 fill-cafe-gold text-cafe-gold\" />\n                        ))}\n                      </div>\n\n                      {/* Content */}\n                      <blockquote className=\"text-cafe-brown leading-relaxed text-center italic\">\n                        &ldquo;{testimonial.content}&rdquo;\n                      </blockquote>\n\n                      {/* Author */}\n                      <div className=\"text-center pt-4 border-t border-cafe-sand\">\n                        <div className=\"w-16 h-16 mx-auto mb-3 rounded-full overflow-hidden\">\n                          <img\n                            src={testimonial.image}\n                            alt={testimonial.name}\n                            className=\"w-full h-full object-cover\"\n                          />\n                        </div>\n                        <h4 className=\"font-serif font-semibold text-cafe-dark-brown\">\n                          {testimonial.name}\n                        </h4>\n                        <p className=\"text-sm text-cafe-brown\">\n                          {testimonial.role}\n                        </p>\n                        <p className=\"text-xs text-cafe-brown mt-1\">\n                          {new Date(testimonial.date).toLocaleDateString()}\n                        </p>\n                      </div>\n                    </CardContent>\n                  </Card>\n                </CarouselItem>\n              ))}\n            </CarouselContent>\n            <CarouselPrevious className=\"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream\" />\n            <CarouselNext className=\"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream\" />\n          </Carousel>\n        </motion.div>\n\n        {/* Stats */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-8 mb-12\"\n        >\n          <div className=\"text-center\">\n            <div className=\"text-3xl md:text-4xl font-bold text-cafe-dark-brown mb-2\">\n              500+\n            </div>\n            <div className=\"text-cafe-brown font-medium\">\n              Happy Customers\n            </div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl md:text-4xl font-bold text-cafe-dark-brown mb-2\">\n              4.9\n            </div>\n            <div className=\"text-cafe-brown font-medium\">\n              Average Rating\n            </div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl md:text-4xl font-bold text-cafe-dark-brown mb-2\">\n              3+\n            </div>\n            <div className=\"text-cafe-brown font-medium\">\n              Years Serving\n            </div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl md:text-4xl font-bold text-cafe-dark-brown mb-2\">\n              50+\n            </div>\n            <div className=\"text-cafe-brown font-medium\">\n              Menu Items\n            </div>\n          </div>\n        </motion.div>\n\n        {/* CTA Button */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center\"\n        >\n          <Link href=\"/testimonials\">\n            <Button \n              size=\"lg\" \n              className=\"bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream px-8 py-3 text-lg font-semibold shadow-cafe-lg\"\n            >\n              Read All Reviews\n            </Button>\n          </Link>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAOA;AAAA;AACA;AACA;AAdA;;;;;;;;;AAiBO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAsE;;;;;;sCAGpF,8OAAC;4BAAE,WAAU;sCAA4D;;;;;;;;;;;;8BAM3E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wBACP,MAAM;4BACJ,OAAO;4BACP,MAAM;wBACR;wBACA,WAAU;;0CAEV,8OAAC,oIAAA,CAAA,kBAAe;gCAAC,WAAU;0CACxB,2HAAA,CAAA,uBAAoB,CAAC,GAAG,CAAC,CAAC,4BACzB,8OAAC,oIAAA,CAAA,eAAY;wCAAsB,WAAU;kDAC3C,cAAA,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEAErB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAKrB,8OAAC;wDAAI,WAAU;kEACZ;+DAAI,MAAM,YAAY,MAAM;yDAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtC,8OAAC,kMAAA,CAAA,OAAI;gEAAS,WAAU;+DAAb;;;;;;;;;;kEAKf,8OAAC;wDAAW,WAAU;;4DAAqD;4DACjE,YAAY,OAAO;4DAAC;;;;;;;kEAI9B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,KAAK,YAAY,KAAK;oEACtB,KAAK,YAAY,IAAI;oEACrB,WAAU;;;;;;;;;;;0EAGd,8OAAC;gEAAG,WAAU;0EACX,YAAY,IAAI;;;;;;0EAEnB,8OAAC;gEAAE,WAAU;0EACV,YAAY,IAAI;;;;;;0EAEnB,8OAAC;gEAAE,WAAU;0EACV,IAAI,KAAK,YAAY,IAAI,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;uCAtCrC,YAAY,EAAE;;;;;;;;;;0CA8CrC,8OAAC,oIAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;0CAC5B,8OAAC,oIAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA2D;;;;;;8CAG1E,8OAAC;oCAAI,WAAU;8CAA8B;;;;;;;;;;;;sCAI/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA2D;;;;;;8CAG1E,8OAAC;oCAAI,WAAU;8CAA8B;;;;;;;;;;;;sCAI/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA2D;;;;;;8CAG1E,8OAAC;oCAAI,WAAU;8CAA8B;;;;;;;;;;;;sCAI/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA2D;;;;;;8CAG1E,8OAAC;oCAAI,WAAU;8CAA8B;;;;;;;;;;;;;;;;;;8BAOjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 3591, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3617, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  useFormState,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues,\n} from \"react-hook-form\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Label } from \"@/components/ui/label\"\n\nconst Form = FormProvider\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n> = {\n  name: TName\n}\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n)\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  )\n}\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext)\n  const itemContext = React.useContext(FormItemContext)\n  const { getFieldState } = useFormContext()\n  const formState = useFormState({ name: fieldContext.name })\n  const fieldState = getFieldState(fieldContext.name, formState)\n\n  if (!fieldContext) {\n    throw new Error(\"useFormField should be used within <FormField>\")\n  }\n\n  const { id } = itemContext\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  }\n}\n\ntype FormItemContextValue = {\n  id: string\n}\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n)\n\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\n  const id = React.useId()\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div\n        data-slot=\"form-item\"\n        className={cn(\"grid gap-2\", className)}\n        {...props}\n      />\n    </FormItemContext.Provider>\n  )\n}\n\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const { error, formItemId } = useFormField()\n\n  return (\n    <Label\n      data-slot=\"form-label\"\n      data-error={!!error}\n      className={cn(\"data-[error=true]:text-destructive\", className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  )\n}\n\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\n\n  return (\n    <Slot\n      data-slot=\"form-control\"\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  )\n}\n\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { formDescriptionId } = useFormField()\n\n  return (\n    <p\n      data-slot=\"form-description\"\n      id={formDescriptionId}\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { error, formMessageId } = useFormField()\n  const body = error ? String(error?.message ?? \"\") : props.children\n\n  if (!body) {\n    return null\n  }\n\n  return (\n    <p\n      data-slot=\"form-message\"\n      id={formMessageId}\n      className={cn(\"text-destructive text-sm\", className)}\n      {...props}\n    >\n      {body}\n    </p>\n  )\n}\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;AAhBA;;;;;;;AAkBA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,qMAAA,CAAA,gBAAmB,CAC1C,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,qMAAA,CAAA,aAAgB,CAAC;IACtC,MAAM,cAAc,qMAAA,CAAA,aAAgB,CAAC;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,qMAAA,CAAA,gBAAmB,CACzC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,MAAM,KAAK,qMAAA,CAAA,QAAW;IAEtB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,GAAG,OAA0C;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,8OAAC,gKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 3767, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3791, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3814, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/contact-form.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport * as z from \"zod\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  Form,\n  FormControl,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Send, CheckCircle } from \"lucide-react\";\n\nconst contactFormSchema = z.object({\n  name: z.string().min(2, \"Name must be at least 2 characters\"),\n  email: z.string().email(\"Please enter a valid email address\"),\n  phone: z.string().optional(),\n  subject: z.string().min(5, \"Subject must be at least 5 characters\"),\n  message: z.string().min(10, \"Message must be at least 10 characters\"),\n});\n\ntype ContactFormData = z.infer<typeof contactFormSchema>;\n\nexport function ContactForm() {\n  const [isSubmitted, setIsSubmitted] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const form = useForm<ContactFormData>({\n    resolver: zodResolver(contactFormSchema),\n    defaultValues: {\n      name: \"\",\n      email: \"\",\n      phone: \"\",\n      subject: \"\",\n      message: \"\",\n    },\n  });\n\n  const onSubmit = async (data: ContactFormData) => {\n    setIsSubmitting(true);\n    \n    // Simulate form submission (since this is static)\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    \n    console.log(\"Contact form data:\", data);\n    setIsSubmitted(true);\n    setIsSubmitting(false);\n    form.reset();\n  };\n\n  if (isSubmitted) {\n    return (\n      <motion.div\n        initial={{ opacity: 0, scale: 0.9 }}\n        animate={{ opacity: 1, scale: 1 }}\n        className=\"text-center py-12\"\n      >\n        <div className=\"w-16 h-16 bg-cafe-green rounded-full flex items-center justify-center mx-auto mb-4\">\n          <CheckCircle className=\"h-8 w-8 text-white\" />\n        </div>\n        <h3 className=\"font-serif text-2xl font-semibold text-cafe-dark-brown mb-2\">\n          Message Sent Successfully!\n        </h3>\n        <p className=\"text-cafe-brown mb-6\">\n          Thank you for reaching out. We&apos;ll get back to you within 24 hours.\n        </p>\n        <Button\n          onClick={() => setIsSubmitted(false)}\n          variant=\"outline\"\n          className=\"border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream\"\n        >\n          Send Another Message\n        </Button>\n      </motion.div>\n    );\n  }\n\n  return (\n    <Card className=\"border-cafe-beige shadow-cafe-lg\">\n      <CardHeader>\n        <CardTitle className=\"font-serif text-2xl text-cafe-dark-brown text-center\">\n          Get in Touch\n        </CardTitle>\n        <p className=\"text-cafe-brown text-center\">\n          Have a question or want to make a reservation? We&apos;d love to hear from you!\n        </p>\n      </CardHeader>\n      <CardContent>\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            {/* Name Field */}\n            <FormField\n              control={form.control}\n              name=\"name\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-cafe-dark-brown font-medium\">\n                    Full Name *\n                  </FormLabel>\n                  <FormControl>\n                    <Input\n                      placeholder=\"Enter your full name\"\n                      className=\"border-cafe-beige focus:border-cafe-brown\"\n                      {...field}\n                    />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n\n            {/* Email Field */}\n            <FormField\n              control={form.control}\n              name=\"email\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-cafe-dark-brown font-medium\">\n                    Email Address *\n                  </FormLabel>\n                  <FormControl>\n                    <Input\n                      type=\"email\"\n                      placeholder=\"Enter your email address\"\n                      className=\"border-cafe-beige focus:border-cafe-brown\"\n                      {...field}\n                    />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n\n            {/* Phone Field */}\n            <FormField\n              control={form.control}\n              name=\"phone\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-cafe-dark-brown font-medium\">\n                    Phone Number (Optional)\n                  </FormLabel>\n                  <FormControl>\n                    <Input\n                      type=\"tel\"\n                      placeholder=\"Enter your phone number\"\n                      className=\"border-cafe-beige focus:border-cafe-brown\"\n                      {...field}\n                    />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n\n            {/* Subject Field */}\n            <FormField\n              control={form.control}\n              name=\"subject\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-cafe-dark-brown font-medium\">\n                    Subject *\n                  </FormLabel>\n                  <FormControl>\n                    <Input\n                      placeholder=\"What's this about?\"\n                      className=\"border-cafe-beige focus:border-cafe-brown\"\n                      {...field}\n                    />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n\n            {/* Message Field */}\n            <FormField\n              control={form.control}\n              name=\"message\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-cafe-dark-brown font-medium\">\n                    Message *\n                  </FormLabel>\n                  <FormControl>\n                    <Textarea\n                      placeholder=\"Tell us more about your inquiry...\"\n                      className=\"border-cafe-beige focus:border-cafe-brown min-h-[120px]\"\n                      {...field}\n                    />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n\n            {/* Submit Button */}\n            <Button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className=\"w-full bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream font-semibold py-3\"\n            >\n              {isSubmitting ? (\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"w-4 h-4 border-2 border-cafe-cream border-t-transparent rounded-full animate-spin\" />\n                  Sending...\n                </div>\n              ) : (\n                <div className=\"flex items-center gap-2\">\n                  <Send className=\"h-4 w-4\" />\n                  Send Message\n                </div>\n              )}\n            </Button>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AAAA;AAnBA;;;;;;;;;;;;;AAqBA,MAAM,oBAAoB,+IAAA,CAAA,SAAQ,CAAC;IACjC,MAAM,+IAAA,CAAA,SAAQ,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,+IAAA,CAAA,SAAQ,GAAG,KAAK,CAAC;IACxB,OAAO,+IAAA,CAAA,SAAQ,GAAG,QAAQ;IAC1B,SAAS,+IAAA,CAAA,SAAQ,GAAG,GAAG,CAAC,GAAG;IAC3B,SAAS,+IAAA,CAAA,SAAQ,GAAG,GAAG,CAAC,IAAI;AAC9B;AAIO,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAmB;QACpC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,MAAM;YACN,OAAO;YACP,OAAO;YACP,SAAS;YACT,SAAS;QACX;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAEhB,kDAAkD;QAClD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,QAAQ,GAAG,CAAC,sBAAsB;QAClC,eAAe;QACf,gBAAgB;QAChB,KAAK,KAAK;IACZ;IAEA,IAAI,aAAa;QACf,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAClC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,WAAU;;8BAEV,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;8BAEzB,8OAAC;oBAAG,WAAU;8BAA8D;;;;;;8BAG5E,8OAAC;oBAAE,WAAU;8BAAuB;;;;;;8BAGpC,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAS,IAAM,eAAe;oBAC9B,SAAQ;oBACR,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAuD;;;;;;kCAG5E,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;0BAI7C,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAE,GAAG,IAAI;8BACZ,cAAA,8OAAC;wBAAK,UAAU,KAAK,YAAY,CAAC;wBAAW,WAAU;;0CAErD,8OAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0DACP,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAmC;;;;;;0DAGxD,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,WAAU;oDACT,GAAG,KAAK;;;;;;;;;;;0DAGb,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAMlB,8OAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0DACP,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAmC;;;;;;0DAGxD,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,aAAY;oDACZ,WAAU;oDACT,GAAG,KAAK;;;;;;;;;;;0DAGb,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAMlB,8OAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0DACP,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAmC;;;;;;0DAGxD,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,aAAY;oDACZ,WAAU;oDACT,GAAG,KAAK;;;;;;;;;;;0DAGb,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAMlB,8OAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0DACP,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAmC;;;;;;0DAGxD,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,WAAU;oDACT,GAAG,KAAK;;;;;;;;;;;0DAGb,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAMlB,8OAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0DACP,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAmC;;;;;;0DAGxD,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oDACP,aAAY;oDACZ,WAAU;oDACT,GAAG,KAAK;;;;;;;;;;;0DAGb,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAMlB,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,6BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;wCAAsF;;;;;;yDAIvG,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9C", "debugId": null}}, {"offset": {"line": 4256, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/contact-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { MapPin, Phone, Mail, Clock } from \"lucide-react\";\nimport { ContactForm } from \"./contact-form\";\nimport { cafeInfo } from \"@/data/cafe-info\";\n\nexport function ContactSection() {\n  return (\n    <section className=\"py-20 bg-cafe-cream\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6\">\n            Visit Us Today\n          </h2>\n          <p className=\"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed\">\n            We&apos;d love to welcome you to our cozy café. Drop by for a cup of coffee or get in touch with us for any inquiries.\n          </p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12\">\n          {/* Contact Form */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n          >\n            <ContactForm />\n          </motion.div>\n\n          {/* Contact Information */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"space-y-6\"\n          >\n            {/* Contact Details */}\n            <Card className=\"border-cafe-beige shadow-cafe\">\n              <CardContent className=\"p-6 space-y-6\">\n                <h3 className=\"font-serif text-2xl font-semibold text-cafe-dark-brown\">\n                  Contact Details\n                </h3>\n                \n                <div className=\"space-y-4\">\n                  <div className=\"flex items-start gap-4\">\n                    <div className=\"w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0\">\n                      <MapPin className=\"h-6 w-6 text-cafe-brown\" />\n                    </div>\n                    <div>\n                      <h4 className=\"font-semibold text-cafe-dark-brown mb-1\">Address</h4>\n                      <p className=\"text-cafe-brown\">{cafeInfo.contact.address}</p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-start gap-4\">\n                    <div className=\"w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0\">\n                      <Phone className=\"h-6 w-6 text-cafe-brown\" />\n                    </div>\n                    <div>\n                      <h4 className=\"font-semibold text-cafe-dark-brown mb-1\">Phone</h4>\n                      <a \n                        href={`tel:${cafeInfo.contact.phone}`}\n                        className=\"text-cafe-brown hover:text-cafe-dark-brown transition-colors\"\n                      >\n                        {cafeInfo.contact.phone}\n                      </a>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-start gap-4\">\n                    <div className=\"w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0\">\n                      <Mail className=\"h-6 w-6 text-cafe-brown\" />\n                    </div>\n                    <div>\n                      <h4 className=\"font-semibold text-cafe-dark-brown mb-1\">Email</h4>\n                      <a \n                        href={`mailto:${cafeInfo.contact.email}`}\n                        className=\"text-cafe-brown hover:text-cafe-dark-brown transition-colors\"\n                      >\n                        {cafeInfo.contact.email}\n                      </a>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Opening Hours */}\n            <Card className=\"border-cafe-beige shadow-cafe\">\n              <CardContent className=\"p-6 space-y-6\">\n                <h3 className=\"font-serif text-2xl font-semibold text-cafe-dark-brown\">\n                  Opening Hours\n                </h3>\n                \n                <div className=\"space-y-4\">\n                  <div className=\"flex items-start gap-4\">\n                    <div className=\"w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0\">\n                      <Clock className=\"h-6 w-6 text-cafe-brown\" />\n                    </div>\n                    <div className=\"flex-1\">\n                      <div className=\"flex justify-between items-center mb-2\">\n                        <span className=\"font-semibold text-cafe-dark-brown\">Monday - Friday</span>\n                        <span className=\"text-cafe-brown\">{cafeInfo.hours.weekdays}</span>\n                      </div>\n                      <div className=\"flex justify-between items-center mb-2\">\n                        <span className=\"font-semibold text-cafe-dark-brown\">Saturday - Sunday</span>\n                        <span className=\"text-cafe-brown\">{cafeInfo.hours.weekends}</span>\n                      </div>\n                      <div className=\"flex justify-between items-center\">\n                        <span className=\"font-semibold text-cafe-dark-brown\">Public Holidays</span>\n                        <span className=\"text-cafe-brown\">{cafeInfo.hours.holidays}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Map Placeholder */}\n            <Card className=\"border-cafe-beige shadow-cafe\">\n              <CardContent className=\"p-0\">\n                <div className=\"aspect-video bg-cafe-beige rounded-lg overflow-hidden\">\n                  <div className=\"w-full h-full flex items-center justify-center text-cafe-brown\">\n                    <div className=\"text-center\">\n                      <MapPin className=\"h-12 w-12 mx-auto mb-2\" />\n                      <p className=\"font-semibold\">Interactive Map</p>\n                      <p className=\"text-sm\">Find us on Main Road, Biratnagar</p>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAsE;;;;;;sCAGpF,8OAAC;4BAAE,WAAU;sCAA4D;;;;;;;;;;;;8BAK3E,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sCAId,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAGV,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DAAyD;;;;;;0DAIvE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;0EAEpB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA0C;;;;;;kFACxD,8OAAC;wEAAE,WAAU;kFAAmB,2HAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;;kEAI5D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;0EAEnB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA0C;;;;;;kFACxD,8OAAC;wEACC,MAAM,CAAC,IAAI,EAAE,2HAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,KAAK,EAAE;wEACrC,WAAU;kFAET,2HAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;kEAK7B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA0C;;;;;;kFACxD,8OAAC;wEACC,MAAM,CAAC,OAAO,EAAE,2HAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,KAAK,EAAE;wEACxC,WAAU;kFAET,2HAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CASnC,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DAAyD;;;;;;0DAIvE,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAqC;;;;;;sFACrD,8OAAC;4EAAK,WAAU;sFAAmB,2HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,QAAQ;;;;;;;;;;;;8EAE5D,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAqC;;;;;;sFACrD,8OAAC;4EAAK,WAAU;sFAAmB,2HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,QAAQ;;;;;;;;;;;;8EAE5D,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAqC;;;;;;sFACrD,8OAAC;4EAAK,WAAU;sFAAmB,2HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAStE,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,8OAAC;4DAAE,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW7C", "debugId": null}}, {"offset": {"line": 4768, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/cafe/src/components/footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { Coffee, MapPin, Phone, Mail, Clock, Facebook, Instagram, Twitter } from \"lucide-react\";\nimport { cafeInfo } from \"@/data/cafe-info\";\n\nexport function Footer() {\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"bg-cafe-dark-brown text-cafe-cream\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Brand Section */}\n          <div className=\"space-y-4\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <Coffee className=\"h-8 w-8 text-cafe-gold\" />\n              <span className=\"font-serif font-bold text-xl\">\n                Himalayan Brew\n              </span>\n            </Link>\n            <p className=\"text-cafe-beige leading-relaxed\">\n              {cafeInfo.description}\n            </p>\n            <div className=\"flex space-x-4\">\n              <a\n                href={cafeInfo.socialMedia.facebook}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"w-10 h-10 bg-cafe-brown rounded-full flex items-center justify-center hover:bg-cafe-gold transition-colors duration-200\"\n              >\n                <Facebook className=\"h-5 w-5\" />\n              </a>\n              <a\n                href={cafeInfo.socialMedia.instagram}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"w-10 h-10 bg-cafe-brown rounded-full flex items-center justify-center hover:bg-cafe-gold transition-colors duration-200\"\n              >\n                <Instagram className=\"h-5 w-5\" />\n              </a>\n              <a\n                href={cafeInfo.socialMedia.twitter}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"w-10 h-10 bg-cafe-brown rounded-full flex items-center justify-center hover:bg-cafe-gold transition-colors duration-200\"\n              >\n                <Twitter className=\"h-5 w-5\" />\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"space-y-4\">\n            <h3 className=\"font-serif text-lg font-semibold text-cafe-gold\">\n              Quick Links\n            </h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/\" className=\"text-cafe-beige hover:text-cafe-gold transition-colors duration-200\">\n                  Home\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-cafe-beige hover:text-cafe-gold transition-colors duration-200\">\n                  About Us\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/menu\" className=\"text-cafe-beige hover:text-cafe-gold transition-colors duration-200\">\n                  Menu\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/gallery\" className=\"text-cafe-beige hover:text-cafe-gold transition-colors duration-200\">\n                  Gallery\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/testimonials\" className=\"text-cafe-beige hover:text-cafe-gold transition-colors duration-200\">\n                  Testimonials\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-cafe-beige hover:text-cafe-gold transition-colors duration-200\">\n                  Contact\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div className=\"space-y-4\">\n            <h3 className=\"font-serif text-lg font-semibold text-cafe-gold\">\n              Contact Info\n            </h3>\n            <ul className=\"space-y-3\">\n              <li className=\"flex items-start gap-3\">\n                <MapPin className=\"h-5 w-5 text-cafe-gold mt-0.5 flex-shrink-0\" />\n                <span className=\"text-cafe-beige\">\n                  {cafeInfo.contact.address}\n                </span>\n              </li>\n              <li className=\"flex items-center gap-3\">\n                <Phone className=\"h-5 w-5 text-cafe-gold flex-shrink-0\" />\n                <a \n                  href={`tel:${cafeInfo.contact.phone}`}\n                  className=\"text-cafe-beige hover:text-cafe-gold transition-colors duration-200\"\n                >\n                  {cafeInfo.contact.phone}\n                </a>\n              </li>\n              <li className=\"flex items-center gap-3\">\n                <Mail className=\"h-5 w-5 text-cafe-gold flex-shrink-0\" />\n                <a \n                  href={`mailto:${cafeInfo.contact.email}`}\n                  className=\"text-cafe-beige hover:text-cafe-gold transition-colors duration-200\"\n                >\n                  {cafeInfo.contact.email}\n                </a>\n              </li>\n            </ul>\n          </div>\n\n          {/* Opening Hours */}\n          <div className=\"space-y-4\">\n            <h3 className=\"font-serif text-lg font-semibold text-cafe-gold\">\n              Opening Hours\n            </h3>\n            <ul className=\"space-y-2\">\n              <li className=\"flex items-start gap-3\">\n                <Clock className=\"h-5 w-5 text-cafe-gold mt-0.5 flex-shrink-0\" />\n                <div className=\"text-cafe-beige\">\n                  <div className=\"font-medium\">Monday - Friday</div>\n                  <div className=\"text-sm\">{cafeInfo.hours.weekdays}</div>\n                </div>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <Clock className=\"h-5 w-5 text-cafe-gold mt-0.5 flex-shrink-0\" />\n                <div className=\"text-cafe-beige\">\n                  <div className=\"font-medium\">Saturday - Sunday</div>\n                  <div className=\"text-sm\">{cafeInfo.hours.weekends}</div>\n                </div>\n              </li>\n              <li className=\"flex items-start gap-3\">\n                <Clock className=\"h-5 w-5 text-cafe-gold mt-0.5 flex-shrink-0\" />\n                <div className=\"text-cafe-beige\">\n                  <div className=\"font-medium\">Public Holidays</div>\n                  <div className=\"text-sm\">{cafeInfo.hours.holidays}</div>\n                </div>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-cafe-brown mt-12 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-cafe-beige text-sm\">\n            © {currentYear} Himalayan Brew Café. All rights reserved.\n          </p>\n          <div className=\"flex space-x-6 mt-4 md:mt-0\">\n            <Link href=\"/privacy\" className=\"text-cafe-beige hover:text-cafe-gold text-sm transition-colors duration-200\">\n              Privacy Policy\n            </Link>\n            <Link href=\"/terms\" className=\"text-cafe-beige hover:text-cafe-gold text-sm transition-colors duration-200\">\n              Terms of Service\n            </Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;;8CAIjD,8OAAC;oCAAE,WAAU;8CACV,2HAAA,CAAA,WAAQ,CAAC,WAAW;;;;;;8CAEvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAM,2HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,QAAQ;4CACnC,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CACC,MAAM,2HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,SAAS;4CACpC,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,8OAAC,4MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC;4CACC,MAAM,2HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,OAAO;4CAClC,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAkD;;;;;;8CAGhE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAsE;;;;;;;;;;;sDAIjG,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAsE;;;;;;;;;;;sDAItG,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAsE;;;;;;;;;;;sDAIrG,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAsE;;;;;;;;;;;sDAIxG,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgB,WAAU;0DAAsE;;;;;;;;;;;sDAI7G,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAsE;;;;;;;;;;;;;;;;;;;;;;;sCAQ5G,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAkD;;;;;;8CAGhE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DACb,2HAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,OAAO;;;;;;;;;;;;sDAG7B,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDACC,MAAM,CAAC,IAAI,EAAE,2HAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,KAAK,EAAE;oDACrC,WAAU;8DAET,2HAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;sDAG3B,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDACC,MAAM,CAAC,OAAO,EAAE,2HAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,KAAK,EAAE;oDACxC,WAAU;8DAET,2HAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAO/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAkD;;;;;;8CAGhE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,8OAAC;4DAAI,WAAU;sEAAW,2HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,QAAQ;;;;;;;;;;;;;;;;;;sDAGrD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,8OAAC;4DAAI,WAAU;sEAAW,2HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,QAAQ;;;;;;;;;;;;;;;;;;sDAGrD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,8OAAC;4DAAI,WAAU;sEAAW,2HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ3D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;gCAA0B;gCAClC;gCAAY;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAA8E;;;;;;8CAG9G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAA8E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxH", "debugId": null}}]}