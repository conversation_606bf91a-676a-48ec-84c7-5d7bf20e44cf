"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Star, Leaf, Filter } from "lucide-react";
import { menuItems, menuCategories, getItemsByCategory } from "@/data/menu";
import Image from "next/image";

export function MenuCategories() {
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [showFilters, setShowFilters] = useState(false);

  const filteredItems = selectedCategory === "All" 
    ? menuItems 
    : getItemsByCategory(selectedCategory);

  const allCategories = ["All", ...menuCategories];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6">
            Explore Our Menu
          </h2>
          <p className="text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed">
            Every dish and drink is crafted with love, using the finest ingredients and traditional recipes passed down through generations.
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="mb-12"
        >
          {/* Mobile Filter Toggle */}
          <div className="md:hidden mb-4">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="w-full border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filter by Category
            </Button>
          </div>

          {/* Category Buttons */}
          <div className={`${showFilters ? 'block' : 'hidden'} md:block`}>
            <div className="flex flex-wrap justify-center gap-3">
              {allCategories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  onClick={() => setSelectedCategory(category)}
                  className={`${
                    selectedCategory === category
                      ? "bg-cafe-brown text-cafe-cream hover:bg-cafe-dark-brown"
                      : "border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream"
                  } transition-colors duration-200`}
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Menu Items Grid */}
        <AnimatePresence mode="wait">
          <motion.div
            key={selectedCategory}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.4 }}
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {filteredItems.map((item, index) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="h-full overflow-hidden border-cafe-beige hover:shadow-cafe-lg transition-all duration-300 group">
                  {/* Image */}
                  <div className="relative aspect-[4/3] overflow-hidden">
                    <Image
                      src={item.image}
                      alt={item.name}
                      width={400}
                      height={300}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute top-4 left-4 flex gap-2">
                      {item.isPopular && (
                        <Badge className="bg-cafe-gold text-cafe-dark-brown font-semibold">
                          <Star className="h-3 w-3 mr-1" />
                          Popular
                        </Badge>
                      )}
                      {item.isVegetarian && (
                        <Badge variant="secondary" className="bg-cafe-green text-white">
                          <Leaf className="h-3 w-3 mr-1" />
                          Veg
                        </Badge>
                      )}
                      {item.isVegan && (
                        <Badge variant="secondary" className="bg-cafe-dark-green text-white">
                          <Leaf className="h-3 w-3 mr-1" />
                          Vegan
                        </Badge>
                      )}
                    </div>
                    <div className="absolute bottom-4 right-4">
                      <div className="bg-cafe-dark-brown/80 backdrop-blur-sm text-cafe-cream px-3 py-1 rounded-full font-bold">
                        Rs. {item.price}
                      </div>
                    </div>
                  </div>

                  <CardContent className="p-6">
                    <div className="space-y-3">
                      {/* Name and Category */}
                      <div className="flex justify-between items-start">
                        <h3 className="font-serif text-xl font-semibold text-cafe-dark-brown">
                          {item.name}
                        </h3>
                        <Badge variant="outline" className="border-cafe-beige text-cafe-brown text-xs">
                          {item.category}
                        </Badge>
                      </div>

                      {/* Description */}
                      <p className="text-cafe-brown leading-relaxed text-sm">
                        {item.description}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>

        {/* Results Count */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.2 }}
          className="text-center mt-12"
        >
          <p className="text-cafe-brown">
            Showing {filteredItems.length} {filteredItems.length === 1 ? 'item' : 'items'}
            {selectedCategory !== "All" && ` in ${selectedCategory}`}
          </p>
        </motion.div>
      </div>
    </section>
  );
}
