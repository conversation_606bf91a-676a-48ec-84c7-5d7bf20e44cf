"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { MapPin, Phone, Mail, Clock, Facebook, Instagram, Twitter, Navigation } from "lucide-react";
import { cafeInfo } from "@/data/cafe-info";

export function ContactInfo() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6">
            How to Find Us
          </h2>
          <p className="text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed">
            Located in the bustling heart of Biratnagar, we&apos;re easy to find and always ready to welcome you with a warm smile and exceptional coffee.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 mb-16">
          {/* Contact Details */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {/* Address */}
            <Card className="border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0">
                    <MapPin className="h-6 w-6 text-cafe-brown" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-serif text-xl font-semibold text-cafe-dark-brown mb-2">
                      Our Location
                    </h3>
                    <p className="text-cafe-brown leading-relaxed mb-4">
                      {cafeInfo.contact.address}
                    </p>
                    <Button 
                      variant="outline" 
                      size="sm"
                      className="border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream"
                    >
                      <Navigation className="h-4 w-4 mr-2" />
                      Get Directions
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Phone */}
            <Card className="border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0">
                    <Phone className="h-6 w-6 text-cafe-brown" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-serif text-xl font-semibold text-cafe-dark-brown mb-2">
                      Call Us
                    </h3>
                    <p className="text-cafe-brown leading-relaxed mb-4">
                      Have questions or want to make a reservation? Give us a call!
                    </p>
                    <a href={`tel:${cafeInfo.contact.phone}`}>
                      <Button 
                        variant="outline" 
                        size="sm"
                        className="border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream"
                      >
                        <Phone className="h-4 w-4 mr-2" />
                        {cafeInfo.contact.phone}
                      </Button>
                    </a>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Email */}
            <Card className="border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0">
                    <Mail className="h-6 w-6 text-cafe-brown" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-serif text-xl font-semibold text-cafe-dark-brown mb-2">
                      Email Us
                    </h3>
                    <p className="text-cafe-brown leading-relaxed mb-4">
                      Send us your feedback, suggestions, or business inquiries.
                    </p>
                    <a href={`mailto:${cafeInfo.contact.email}`}>
                      <Button 
                        variant="outline" 
                        size="sm"
                        className="border-cafe-brown text-cafe-brown hover:bg-cafe-brown hover:text-cafe-cream"
                      >
                        <Mail className="h-4 w-4 mr-2" />
                        {cafeInfo.contact.email}
                      </Button>
                    </a>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Hours & Social */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {/* Opening Hours */}
            <Card className="border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-cafe-beige rounded-full flex items-center justify-center flex-shrink-0">
                    <Clock className="h-6 w-6 text-cafe-brown" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-serif text-xl font-semibold text-cafe-dark-brown mb-4">
                      Opening Hours
                    </h3>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-cafe-brown font-medium">Monday - Friday</span>
                        <span className="text-cafe-dark-brown font-semibold">{cafeInfo.hours.weekdays}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-cafe-brown font-medium">Saturday - Sunday</span>
                        <span className="text-cafe-dark-brown font-semibold">{cafeInfo.hours.weekends}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-cafe-brown font-medium">Public Holidays</span>
                        <span className="text-cafe-dark-brown font-semibold">{cafeInfo.hours.holidays}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Social Media */}
            <Card className="border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <h3 className="font-serif text-xl font-semibold text-cafe-dark-brown mb-4">
                  Follow Us
                </h3>
                <p className="text-cafe-brown leading-relaxed mb-6">
                  Stay connected with us on social media for updates, special offers, and behind-the-scenes moments.
                </p>
                <div className="flex gap-4">
                  <a
                    href={cafeInfo.socialMedia.facebook}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-12 h-12 bg-cafe-brown rounded-full flex items-center justify-center hover:bg-cafe-dark-brown transition-colors duration-200 text-cafe-cream"
                  >
                    <Facebook className="h-5 w-5" />
                  </a>
                  <a
                    href={cafeInfo.socialMedia.instagram}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-12 h-12 bg-cafe-brown rounded-full flex items-center justify-center hover:bg-cafe-dark-brown transition-colors duration-200 text-cafe-cream"
                  >
                    <Instagram className="h-5 w-5" />
                  </a>
                  <a
                    href={cafeInfo.socialMedia.twitter}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-12 h-12 bg-cafe-brown rounded-full flex items-center justify-center hover:bg-cafe-dark-brown transition-colors duration-200 text-cafe-cream"
                  >
                    <Twitter className="h-5 w-5" />
                  </a>
                </div>
              </CardContent>
            </Card>

            {/* Special Note */}
            <Card className="border-cafe-beige bg-cafe-cream">
              <CardContent className="p-6 text-center">
                <h3 className="font-serif text-lg font-semibold text-cafe-dark-brown mb-3">
                  Special Events & Private Bookings
                </h3>
                <p className="text-cafe-brown text-sm leading-relaxed mb-4">
                  Planning a special event? We offer private bookings for birthdays, meetings, and celebrations. Contact us to discuss your requirements.
                </p>
                <Button 
                  size="sm"
                  className="bg-cafe-brown hover:bg-cafe-dark-brown text-cafe-cream"
                >
                  Inquire About Events
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
