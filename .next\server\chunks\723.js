"use strict";exports.id=723,exports.ids=[723],exports.modules={41312:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},51361:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},97022:(a,b,c)=>{c.d(b,{bm:()=>be,UC:()=>bd,hJ:()=>bc,ZL:()=>bb,bL:()=>a9,l9:()=>ba});var d,e,f,g=c(43210),h=c.t(g,2);function i(a,b,{checkForDefaultPrevented:c=!0}={}){return function(d){if(a?.(d),!1===c||!d.defaultPrevented)return b?.(d)}}var j=c(98599),k=c(60687),l=globalThis?.document?g.useLayoutEffect:()=>{},m=h[" useId ".trim().toString()]||(()=>void 0),n=0;function o(a){let[b,c]=g.useState(m());return l(()=>{a||c(a=>a??String(n++))},[a]),a||(b?`radix-${b}`:"")}var p=h[" useInsertionEffect ".trim().toString()]||l;Symbol("RADIX:SYNC_STATE");var q=c(14163);function r(a){let b=g.useRef(a);return g.useEffect(()=>{b.current=a}),g.useMemo(()=>(...a)=>b.current?.(...a),[])}var s="dismissableLayer.update",t=g.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),u=g.forwardRef((a,b)=>{let{disableOutsidePointerEvents:c=!1,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:h,onInteractOutside:l,onDismiss:m,...n}=a,o=g.useContext(t),[p,u]=g.useState(null),x=p?.ownerDocument??globalThis?.document,[,y]=g.useState({}),z=(0,j.s)(b,a=>u(a)),A=Array.from(o.layers),[B]=[...o.layersWithOutsidePointerEventsDisabled].slice(-1),C=A.indexOf(B),D=p?A.indexOf(p):-1,E=o.layersWithOutsidePointerEventsDisabled.size>0,F=D>=C,G=function(a,b=globalThis?.document){let c=r(a),d=g.useRef(!1),e=g.useRef(()=>{});return g.useEffect(()=>{let a=a=>{if(a.target&&!d.current){let d=function(){w("dismissableLayer.pointerDownOutside",c,f,{discrete:!0})},f={originalEvent:a};"touch"===a.pointerType?(b.removeEventListener("click",e.current),e.current=d,b.addEventListener("click",e.current,{once:!0})):d()}else b.removeEventListener("click",e.current);d.current=!1},f=window.setTimeout(()=>{b.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(f),b.removeEventListener("pointerdown",a),b.removeEventListener("click",e.current)}},[b,c]),{onPointerDownCapture:()=>d.current=!0}}(a=>{let b=a.target,c=[...o.branches].some(a=>a.contains(b));F&&!c&&(f?.(a),l?.(a),a.defaultPrevented||m?.())},x),H=function(a,b=globalThis?.document){let c=r(a),d=g.useRef(!1);return g.useEffect(()=>{let a=a=>{a.target&&!d.current&&w("dismissableLayer.focusOutside",c,{originalEvent:a},{discrete:!1})};return b.addEventListener("focusin",a),()=>b.removeEventListener("focusin",a)},[b,c]),{onFocusCapture:()=>d.current=!0,onBlurCapture:()=>d.current=!1}}(a=>{let b=a.target;![...o.branches].some(a=>a.contains(b))&&(h?.(a),l?.(a),a.defaultPrevented||m?.())},x);return!function(a,b=globalThis?.document){let c=r(a);g.useEffect(()=>{let a=a=>{"Escape"===a.key&&c(a)};return b.addEventListener("keydown",a,{capture:!0}),()=>b.removeEventListener("keydown",a,{capture:!0})},[c,b])}(a=>{D===o.layers.size-1&&(d?.(a),!a.defaultPrevented&&m&&(a.preventDefault(),m()))},x),g.useEffect(()=>{if(p)return c&&(0===o.layersWithOutsidePointerEventsDisabled.size&&(e=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),o.layersWithOutsidePointerEventsDisabled.add(p)),o.layers.add(p),v(),()=>{c&&1===o.layersWithOutsidePointerEventsDisabled.size&&(x.body.style.pointerEvents=e)}},[p,x,c,o]),g.useEffect(()=>()=>{p&&(o.layers.delete(p),o.layersWithOutsidePointerEventsDisabled.delete(p),v())},[p,o]),g.useEffect(()=>{let a=()=>y({});return document.addEventListener(s,a),()=>document.removeEventListener(s,a)},[]),(0,k.jsx)(q.sG.div,{...n,ref:z,style:{pointerEvents:E?F?"auto":"none":void 0,...a.style},onFocusCapture:i(a.onFocusCapture,H.onFocusCapture),onBlurCapture:i(a.onBlurCapture,H.onBlurCapture),onPointerDownCapture:i(a.onPointerDownCapture,G.onPointerDownCapture)})});function v(){let a=new CustomEvent(s);document.dispatchEvent(a)}function w(a,b,c,{discrete:d}){let e=c.originalEvent.target,f=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:c});b&&e.addEventListener(a,b,{once:!0}),d?(0,q.hO)(e,f):e.dispatchEvent(f)}u.displayName="DismissableLayer",g.forwardRef((a,b)=>{let c=g.useContext(t),d=g.useRef(null),e=(0,j.s)(b,d);return g.useEffect(()=>{let a=d.current;if(a)return c.branches.add(a),()=>{c.branches.delete(a)}},[c.branches]),(0,k.jsx)(q.sG.div,{...a,ref:e})}).displayName="DismissableLayerBranch";var x="focusScope.autoFocusOnMount",y="focusScope.autoFocusOnUnmount",z={bubbles:!1,cancelable:!0},A=g.forwardRef((a,b)=>{let{loop:c=!1,trapped:d=!1,onMountAutoFocus:e,onUnmountAutoFocus:f,...h}=a,[i,l]=g.useState(null),m=r(e),n=r(f),o=g.useRef(null),p=(0,j.s)(b,a=>l(a)),s=g.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;g.useEffect(()=>{if(d){let a=function(a){if(s.paused||!i)return;let b=a.target;i.contains(b)?o.current=b:D(o.current,{select:!0})},b=function(a){if(s.paused||!i)return;let b=a.relatedTarget;null!==b&&(i.contains(b)||D(o.current,{select:!0}))};document.addEventListener("focusin",a),document.addEventListener("focusout",b);let c=new MutationObserver(function(a){if(document.activeElement===document.body)for(let b of a)b.removedNodes.length>0&&D(i)});return i&&c.observe(i,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",a),document.removeEventListener("focusout",b),c.disconnect()}}},[d,i,s.paused]),g.useEffect(()=>{if(i){E.add(s);let a=document.activeElement;if(!i.contains(a)){let b=new CustomEvent(x,z);i.addEventListener(x,m),i.dispatchEvent(b),b.defaultPrevented||(function(a,{select:b=!1}={}){let c=document.activeElement;for(let d of a)if(D(d,{select:b}),document.activeElement!==c)return}(B(i).filter(a=>"A"!==a.tagName),{select:!0}),document.activeElement===a&&D(i))}return()=>{i.removeEventListener(x,m),setTimeout(()=>{let b=new CustomEvent(y,z);i.addEventListener(y,n),i.dispatchEvent(b),b.defaultPrevented||D(a??document.body,{select:!0}),i.removeEventListener(y,n),E.remove(s)},0)}}},[i,m,n,s]);let t=g.useCallback(a=>{if(!c&&!d||s.paused)return;let b="Tab"===a.key&&!a.altKey&&!a.ctrlKey&&!a.metaKey,e=document.activeElement;if(b&&e){let b=a.currentTarget,[d,f]=function(a){let b=B(a);return[C(b,a),C(b.reverse(),a)]}(b);d&&f?a.shiftKey||e!==f?a.shiftKey&&e===d&&(a.preventDefault(),c&&D(f,{select:!0})):(a.preventDefault(),c&&D(d,{select:!0})):e===b&&a.preventDefault()}},[c,d,s.paused]);return(0,k.jsx)(q.sG.div,{tabIndex:-1,...h,ref:p,onKeyDown:t})});function B(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}function C(a,b){for(let c of a)if(!function(a,{upTo:b}){if("hidden"===getComputedStyle(a).visibility)return!0;for(;a&&(void 0===b||a!==b);){if("none"===getComputedStyle(a).display)return!0;a=a.parentElement}return!1}(c,{upTo:b}))return c}function D(a,{select:b=!1}={}){if(a&&a.focus){var c;let d=document.activeElement;a.focus({preventScroll:!0}),a!==d&&(c=a)instanceof HTMLInputElement&&"select"in c&&b&&a.select()}}A.displayName="FocusScope";var E=function(){let a=[];return{add(b){let c=a[0];b!==c&&c?.pause(),(a=F(a,b)).unshift(b)},remove(b){a=F(a,b),a[0]?.resume()}}}();function F(a,b){let c=[...a],d=c.indexOf(b);return -1!==d&&c.splice(d,1),c}var G=c(51215),H=g.forwardRef((a,b)=>{let{container:c,...d}=a,[e,f]=g.useState(!1);l(()=>f(!0),[]);let h=c||e&&globalThis?.document?.body;return h?G.createPortal((0,k.jsx)(q.sG.div,{...d,ref:b}),h):null});H.displayName="Portal";var I=a=>{let{present:b,children:c}=a,d=function(a){var b,c;let[d,e]=g.useState(),f=g.useRef(null),h=g.useRef(a),i=g.useRef("none"),[j,k]=(b=a?"mounted":"unmounted",c={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},g.useReducer((a,b)=>c[a][b]??a,b));return g.useEffect(()=>{let a=J(f.current);i.current="mounted"===j?a:"none"},[j]),l(()=>{let b=f.current,c=h.current;if(c!==a){let d=i.current,e=J(b);a?k("MOUNT"):"none"===e||b?.display==="none"?k("UNMOUNT"):c&&d!==e?k("ANIMATION_OUT"):k("UNMOUNT"),h.current=a}},[a,k]),l(()=>{if(d){let a,b=d.ownerDocument.defaultView??window,c=c=>{let e=J(f.current).includes(c.animationName);if(c.target===d&&e&&(k("ANIMATION_END"),!h.current)){let c=d.style.animationFillMode;d.style.animationFillMode="forwards",a=b.setTimeout(()=>{"forwards"===d.style.animationFillMode&&(d.style.animationFillMode=c)})}},e=a=>{a.target===d&&(i.current=J(f.current))};return d.addEventListener("animationstart",e),d.addEventListener("animationcancel",c),d.addEventListener("animationend",c),()=>{b.clearTimeout(a),d.removeEventListener("animationstart",e),d.removeEventListener("animationcancel",c),d.removeEventListener("animationend",c)}}k("ANIMATION_END")},[d,k]),{isPresent:["mounted","unmountSuspended"].includes(j),ref:g.useCallback(a=>{f.current=a?getComputedStyle(a):null,e(a)},[])}}(b),e="function"==typeof c?c({present:d.isPresent}):g.Children.only(c),f=(0,j.s)(d.ref,function(a){let b=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=b&&"isReactWarning"in b&&b.isReactWarning;return c?a.ref:(c=(b=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in b&&b.isReactWarning)?a.props.ref:a.props.ref||a.ref}(e));return"function"==typeof c||d.isPresent?g.cloneElement(e,{ref:f}):null};function J(a){return a?.animationName||"none"}I.displayName="Presence";var K=0;function L(){let a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}var M=function(){return(M=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};function N(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c}Object.create;Object.create;var O=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),P="width-before-scroll-bar";function Q(a,b){return"function"==typeof a?a(b):a&&(a.current=b),a}var R="undefined"!=typeof window?g.useLayoutEffect:g.useEffect,S=new WeakMap;function T(a){return a}var U=function(a){void 0===a&&(a={});var b,c,d,e=(void 0===b&&(b=T),c=[],d=!1,{read:function(){if(d)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:null},useMedium:function(a){var e=b(a,d);return c.push(e),function(){c=c.filter(function(a){return a!==e})}},assignSyncMedium:function(a){for(d=!0;c.length;){var b=c;c=[],b.forEach(a)}c={push:function(b){return a(b)},filter:function(){return c}}},assignMedium:function(a){d=!0;var b=[];if(c.length){var e=c;c=[],e.forEach(a),b=c}var f=function(){var c=b;b=[],c.forEach(a)},g=function(){return Promise.resolve().then(f)};g(),c={push:function(a){b.push(a),g()},filter:function(a){return b=b.filter(a),c}}}});return e.options=M({async:!0,ssr:!1},a),e}(),V=function(){},W=g.forwardRef(function(a,b){var c,d,e,f,h=g.useRef(null),i=g.useState({onScrollCapture:V,onWheelCapture:V,onTouchMoveCapture:V}),j=i[0],k=i[1],l=a.forwardProps,m=a.children,n=a.className,o=a.removeScrollBar,p=a.enabled,q=a.shards,r=a.sideCar,s=a.noRelative,t=a.noIsolation,u=a.inert,v=a.allowPinchZoom,w=a.as,x=a.gapMode,y=N(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),z=(c=[h,b],d=function(a){return c.forEach(function(b){return Q(b,a)})},(e=(0,g.useState)(function(){return{value:null,callback:d,facade:{get current(){return e.value},set current(value){var a=e.value;a!==value&&(e.value=value,e.callback(value,a))}}}})[0]).callback=d,f=e.facade,R(function(){var a=S.get(f);if(a){var b=new Set(a),d=new Set(c),e=f.current;b.forEach(function(a){d.has(a)||Q(a,null)}),d.forEach(function(a){b.has(a)||Q(a,e)})}S.set(f,c)},[c]),f),A=M(M({},y),j);return g.createElement(g.Fragment,null,p&&g.createElement(r,{sideCar:U,removeScrollBar:o,shards:q,noRelative:s,noIsolation:t,inert:u,setCallbacks:k,allowPinchZoom:!!v,lockRef:h,gapMode:x}),l?g.cloneElement(g.Children.only(m),M(M({},A),{ref:z})):g.createElement(void 0===w?"div":w,M({},A,{className:n,ref:z}),m))});W.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},W.classNames={fullWidth:P,zeroRight:O};var X=function(a){var b=a.sideCar,c=N(a,["sideCar"]);if(!b)throw Error("Sidecar: please provide `sideCar` property to import the right car");var d=b.read();if(!d)throw Error("Sidecar medium not found");return g.createElement(d,M({},c))};X.isSideCarExport=!0;var Y=function(){var a=0,b=null;return{add:function(d){if(0==a&&(b=function(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var b=f||c.nc;return b&&a.setAttribute("nonce",b),a}())){var e,g;(e=b).styleSheet?e.styleSheet.cssText=d:e.appendChild(document.createTextNode(d)),g=b,(document.head||document.getElementsByTagName("head")[0]).appendChild(g)}a++},remove:function(){--a||!b||(b.parentNode&&b.parentNode.removeChild(b),b=null)}}},Z=function(){var a=Y();return function(b,c){g.useEffect(function(){return a.add(b),function(){a.remove()}},[b&&c])}},$=function(){var a=Z();return function(b){return a(b.styles,b.dynamic),null}},_={left:0,top:0,right:0,gap:0},aa=function(a){return parseInt(a||"",10)||0},ab=function(a){var b=window.getComputedStyle(document.body),c=b["padding"===a?"paddingLeft":"marginLeft"],d=b["padding"===a?"paddingTop":"marginTop"],e=b["padding"===a?"paddingRight":"marginRight"];return[aa(c),aa(d),aa(e)]},ac=function(a){if(void 0===a&&(a="margin"),"undefined"==typeof window)return _;var b=ab(a),c=document.documentElement.clientWidth,d=window.innerWidth;return{left:b[0],top:b[1],right:b[2],gap:Math.max(0,d-c+b[2]-b[0])}},ad=$(),ae="data-scroll-locked",af=function(a,b,c,d){var e=a.left,f=a.top,g=a.right,h=a.gap;return void 0===c&&(c="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(d,";\n   padding-right: ").concat(h,"px ").concat(d,";\n  }\n  body[").concat(ae,"] {\n    overflow: hidden ").concat(d,";\n    overscroll-behavior: contain;\n    ").concat([b&&"position: relative ".concat(d,";"),"margin"===c&&"\n    padding-left: ".concat(e,"px;\n    padding-top: ").concat(f,"px;\n    padding-right: ").concat(g,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(h,"px ").concat(d,";\n    "),"padding"===c&&"padding-right: ".concat(h,"px ").concat(d,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(O," {\n    right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(P," {\n    margin-right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(O," .").concat(O," {\n    right: 0 ").concat(d,";\n  }\n  \n  .").concat(P," .").concat(P," {\n    margin-right: 0 ").concat(d,";\n  }\n  \n  body[").concat(ae,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(h,"px;\n  }\n")},ag=function(){var a=parseInt(document.body.getAttribute(ae)||"0",10);return isFinite(a)?a:0},ah=function(){g.useEffect(function(){return document.body.setAttribute(ae,(ag()+1).toString()),function(){var a=ag()-1;a<=0?document.body.removeAttribute(ae):document.body.setAttribute(ae,a.toString())}},[])},ai=function(a){var b=a.noRelative,c=a.noImportant,d=a.gapMode,e=void 0===d?"margin":d;ah();var f=g.useMemo(function(){return ac(e)},[e]);return g.createElement(ad,{styles:af(f,!b,e,c?"":"!important")})},aj=!1;if("undefined"!=typeof window)try{var ak=Object.defineProperty({},"passive",{get:function(){return aj=!0,!0}});window.addEventListener("test",ak,ak),window.removeEventListener("test",ak,ak)}catch(a){aj=!1}var al=!!aj&&{passive:!1},am=function(a,b){if(!(a instanceof Element))return!1;var c=window.getComputedStyle(a);return"hidden"!==c[b]&&(c.overflowY!==c.overflowX||"TEXTAREA"===a.tagName||"visible"!==c[b])},an=function(a,b){var c=b.ownerDocument,d=b;do{if("undefined"!=typeof ShadowRoot&&d instanceof ShadowRoot&&(d=d.host),ao(a,d)){var e=ap(a,d);if(e[1]>e[2])return!0}d=d.parentNode}while(d&&d!==c.body);return!1},ao=function(a,b){return"v"===a?am(b,"overflowY"):am(b,"overflowX")},ap=function(a,b){return"v"===a?[b.scrollTop,b.scrollHeight,b.clientHeight]:[b.scrollLeft,b.scrollWidth,b.clientWidth]},aq=function(a,b,c,d,e){var f,g=(f=window.getComputedStyle(b).direction,"h"===a&&"rtl"===f?-1:1),h=g*d,i=c.target,j=b.contains(i),k=!1,l=h>0,m=0,n=0;do{if(!i)break;var o=ap(a,i),p=o[0],q=o[1]-o[2]-g*p;(p||q)&&ao(a,i)&&(m+=q,n+=p);var r=i.parentNode;i=r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?r.host:r}while(!j&&i!==document.body||j&&(b.contains(i)||b===i));return l&&(e&&1>Math.abs(m)||!e&&h>m)?k=!0:!l&&(e&&1>Math.abs(n)||!e&&-h>n)&&(k=!0),k},ar=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},as=function(a){return[a.deltaX,a.deltaY]},at=function(a){return a&&"current"in a?a.current:a},au=0,av=[];let aw=(d=function(a){var b=g.useRef([]),c=g.useRef([0,0]),d=g.useRef(),e=g.useState(au++)[0],f=g.useState($)[0],h=g.useRef(a);g.useEffect(function(){h.current=a},[a]),g.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(e));var b=(function(a,b,c){if(c||2==arguments.length)for(var d,e=0,f=b.length;e<f;e++)!d&&e in b||(d||(d=Array.prototype.slice.call(b,0,e)),d[e]=b[e]);return a.concat(d||Array.prototype.slice.call(b))})([a.lockRef.current],(a.shards||[]).map(at),!0).filter(Boolean);return b.forEach(function(a){return a.classList.add("allow-interactivity-".concat(e))}),function(){document.body.classList.remove("block-interactivity-".concat(e)),b.forEach(function(a){return a.classList.remove("allow-interactivity-".concat(e))})}}},[a.inert,a.lockRef.current,a.shards]);var i=g.useCallback(function(a,b){if("touches"in a&&2===a.touches.length||"wheel"===a.type&&a.ctrlKey)return!h.current.allowPinchZoom;var e,f=ar(a),g=c.current,i="deltaX"in a?a.deltaX:g[0]-f[0],j="deltaY"in a?a.deltaY:g[1]-f[1],k=a.target,l=Math.abs(i)>Math.abs(j)?"h":"v";if("touches"in a&&"h"===l&&"range"===k.type)return!1;var m=an(l,k);if(!m)return!0;if(m?e=l:(e="v"===l?"h":"v",m=an(l,k)),!m)return!1;if(!d.current&&"changedTouches"in a&&(i||j)&&(d.current=e),!e)return!0;var n=d.current||e;return aq(n,b,a,"h"===n?i:j,!0)},[]),j=g.useCallback(function(a){if(av.length&&av[av.length-1]===f){var c="deltaY"in a?as(a):ar(a),d=b.current.filter(function(b){var d;return b.name===a.type&&(b.target===a.target||a.target===b.shadowParent)&&(d=b.delta,d[0]===c[0]&&d[1]===c[1])})[0];if(d&&d.should){a.cancelable&&a.preventDefault();return}if(!d){var e=(h.current.shards||[]).map(at).filter(Boolean).filter(function(b){return b.contains(a.target)});(e.length>0?i(a,e[0]):!h.current.noIsolation)&&a.cancelable&&a.preventDefault()}}},[]),k=g.useCallback(function(a,c,d,e){var f={name:a,delta:c,target:d,should:e,shadowParent:function(a){for(var b=null;null!==a;)a instanceof ShadowRoot&&(b=a.host,a=a.host),a=a.parentNode;return b}(d)};b.current.push(f),setTimeout(function(){b.current=b.current.filter(function(a){return a!==f})},1)},[]),l=g.useCallback(function(a){c.current=ar(a),d.current=void 0},[]),m=g.useCallback(function(b){k(b.type,as(b),b.target,i(b,a.lockRef.current))},[]),n=g.useCallback(function(b){k(b.type,ar(b),b.target,i(b,a.lockRef.current))},[]);g.useEffect(function(){return av.push(f),a.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:n}),document.addEventListener("wheel",j,al),document.addEventListener("touchmove",j,al),document.addEventListener("touchstart",l,al),function(){av=av.filter(function(a){return a!==f}),document.removeEventListener("wheel",j,al),document.removeEventListener("touchmove",j,al),document.removeEventListener("touchstart",l,al)}},[]);var o=a.removeScrollBar,p=a.inert;return g.createElement(g.Fragment,null,p?g.createElement(f,{styles:"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")}):null,o?g.createElement(ai,{noRelative:a.noRelative,gapMode:a.gapMode}):null)},U.useMedium(d),X);var ax=g.forwardRef(function(a,b){return g.createElement(W,M({},a,{ref:b,sideCar:aw}))});ax.classNames=W.classNames;var ay=new WeakMap,az=new WeakMap,aA={},aB=0,aC=function(a){return a&&(a.host||aC(a.parentNode))},aD=function(a,b,c,d){var e=(Array.isArray(a)?a:[a]).map(function(a){if(b.contains(a))return a;var c=aC(a);return c&&b.contains(c)?c:(console.error("aria-hidden",a,"in not contained inside",b,". Doing nothing"),null)}).filter(function(a){return!!a});aA[c]||(aA[c]=new WeakMap);var f=aA[c],g=[],h=new Set,i=new Set(e),j=function(a){!a||h.has(a)||(h.add(a),j(a.parentNode))};e.forEach(j);var k=function(a){!a||i.has(a)||Array.prototype.forEach.call(a.children,function(a){if(h.has(a))k(a);else try{var b=a.getAttribute(d),e=null!==b&&"false"!==b,i=(ay.get(a)||0)+1,j=(f.get(a)||0)+1;ay.set(a,i),f.set(a,j),g.push(a),1===i&&e&&az.set(a,!0),1===j&&a.setAttribute(c,"true"),e||a.setAttribute(d,"true")}catch(b){console.error("aria-hidden: cannot operate on ",a,b)}})};return k(b),h.clear(),aB++,function(){g.forEach(function(a){var b=ay.get(a)-1,e=f.get(a)-1;ay.set(a,b),f.set(a,e),b||(az.has(a)||a.removeAttribute(d),az.delete(a)),e||a.removeAttribute(c)}),--aB||(ay=new WeakMap,ay=new WeakMap,az=new WeakMap,aA={})}},aE=function(a,b,c){void 0===c&&(c="data-aria-hidden");var d=Array.from(Array.isArray(a)?a:[a]),e=b||("undefined"==typeof document?null:(Array.isArray(a)?a[0]:a).ownerDocument.body);return e?(d.push.apply(d,Array.from(e.querySelectorAll("[aria-live], script"))),aD(d,e,c,"aria-hidden")):function(){return null}},aF=c(8730),aG="Dialog",[aH,aI]=function(a,b=[]){let c=[],d=()=>{let b=c.map(a=>g.createContext(a));return function(c){let d=c?.[a]||b;return g.useMemo(()=>({[`__scope${a}`]:{...c,[a]:d}}),[c,d])}};return d.scopeName=a,[function(b,d){let e=g.createContext(d),f=c.length;c=[...c,d];let h=b=>{let{scope:c,children:d,...h}=b,i=c?.[a]?.[f]||e,j=g.useMemo(()=>h,Object.values(h));return(0,k.jsx)(i.Provider,{value:j,children:d})};return h.displayName=b+"Provider",[h,function(c,h){let i=h?.[a]?.[f]||e,j=g.useContext(i);if(j)return j;if(void 0!==d)return d;throw Error(`\`${c}\` must be used within \`${b}\``)}]},function(...a){let b=a[0];if(1===a.length)return b;let c=()=>{let c=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let d=c.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return g.useMemo(()=>({[`__scope${b.scopeName}`]:d}),[d])}};return c.scopeName=b.scopeName,c}(d,...b)]}(aG),[aJ,aK]=aH(aG),aL=a=>{let{__scopeDialog:b,children:c,open:d,defaultOpen:e,onOpenChange:f,modal:h=!0}=a,i=g.useRef(null),j=g.useRef(null),[l,m]=function({prop:a,defaultProp:b,onChange:c=()=>{},caller:d}){let[e,f,h]=function({defaultProp:a,onChange:b}){let[c,d]=g.useState(a),e=g.useRef(c),f=g.useRef(b);return p(()=>{f.current=b},[b]),g.useEffect(()=>{e.current!==c&&(f.current?.(c),e.current=c)},[c,e]),[c,d,f]}({defaultProp:b,onChange:c}),i=void 0!==a,j=i?a:e;{let b=g.useRef(void 0!==a);g.useEffect(()=>{let a=b.current;if(a!==i){let b=i?"controlled":"uncontrolled";console.warn(`${d} is changing from ${a?"controlled":"uncontrolled"} to ${b}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}b.current=i},[i,d])}return[j,g.useCallback(b=>{if(i){let c="function"==typeof b?b(a):b;c!==a&&h.current?.(c)}else f(b)},[i,a,f,h])]}({prop:d,defaultProp:e??!1,onChange:f,caller:aG});return(0,k.jsx)(aJ,{scope:b,triggerRef:i,contentRef:j,contentId:o(),titleId:o(),descriptionId:o(),open:l,onOpenChange:m,onOpenToggle:g.useCallback(()=>m(a=>!a),[m]),modal:h,children:c})};aL.displayName=aG;var aM="DialogTrigger",aN=g.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=aK(aM,c),f=(0,j.s)(b,e.triggerRef);return(0,k.jsx)(q.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":e.open,"aria-controls":e.contentId,"data-state":a3(e.open),...d,ref:f,onClick:i(a.onClick,e.onOpenToggle)})});aN.displayName=aM;var aO="DialogPortal",[aP,aQ]=aH(aO,{forceMount:void 0}),aR=a=>{let{__scopeDialog:b,forceMount:c,children:d,container:e}=a,f=aK(aO,b);return(0,k.jsx)(aP,{scope:b,forceMount:c,children:g.Children.map(d,a=>(0,k.jsx)(I,{present:c||f.open,children:(0,k.jsx)(H,{asChild:!0,container:e,children:a})}))})};aR.displayName=aO;var aS="DialogOverlay",aT=g.forwardRef((a,b)=>{let c=aQ(aS,a.__scopeDialog),{forceMount:d=c.forceMount,...e}=a,f=aK(aS,a.__scopeDialog);return f.modal?(0,k.jsx)(I,{present:d||f.open,children:(0,k.jsx)(aV,{...e,ref:b})}):null});aT.displayName=aS;var aU=(0,aF.TL)("DialogOverlay.RemoveScroll"),aV=g.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=aK(aS,c);return(0,k.jsx)(ax,{as:aU,allowPinchZoom:!0,shards:[e.contentRef],children:(0,k.jsx)(q.sG.div,{"data-state":a3(e.open),...d,ref:b,style:{pointerEvents:"auto",...d.style}})})}),aW="DialogContent",aX=g.forwardRef((a,b)=>{let c=aQ(aW,a.__scopeDialog),{forceMount:d=c.forceMount,...e}=a,f=aK(aW,a.__scopeDialog);return(0,k.jsx)(I,{present:d||f.open,children:f.modal?(0,k.jsx)(aY,{...e,ref:b}):(0,k.jsx)(aZ,{...e,ref:b})})});aX.displayName=aW;var aY=g.forwardRef((a,b)=>{let c=aK(aW,a.__scopeDialog),d=g.useRef(null),e=(0,j.s)(b,c.contentRef,d);return g.useEffect(()=>{let a=d.current;if(a)return aE(a)},[]),(0,k.jsx)(a$,{...a,ref:e,trapFocus:c.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:i(a.onCloseAutoFocus,a=>{a.preventDefault(),c.triggerRef.current?.focus()}),onPointerDownOutside:i(a.onPointerDownOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey;(2===b.button||c)&&a.preventDefault()}),onFocusOutside:i(a.onFocusOutside,a=>a.preventDefault())})}),aZ=g.forwardRef((a,b)=>{let c=aK(aW,a.__scopeDialog),d=g.useRef(!1),e=g.useRef(!1);return(0,k.jsx)(a$,{...a,ref:b,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:b=>{a.onCloseAutoFocus?.(b),b.defaultPrevented||(d.current||c.triggerRef.current?.focus(),b.preventDefault()),d.current=!1,e.current=!1},onInteractOutside:b=>{a.onInteractOutside?.(b),b.defaultPrevented||(d.current=!0,"pointerdown"===b.detail.originalEvent.type&&(e.current=!0));let f=b.target;c.triggerRef.current?.contains(f)&&b.preventDefault(),"focusin"===b.detail.originalEvent.type&&e.current&&b.preventDefault()}})}),a$=g.forwardRef((a,b)=>{let{__scopeDialog:c,trapFocus:d,onOpenAutoFocus:e,onCloseAutoFocus:f,...h}=a,i=aK(aW,c),l=g.useRef(null),m=(0,j.s)(b,l);return g.useEffect(()=>{let a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??L()),document.body.insertAdjacentElement("beforeend",a[1]??L()),K++,()=>{1===K&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),K--}},[]),(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)(A,{asChild:!0,loop:!0,trapped:d,onMountAutoFocus:e,onUnmountAutoFocus:f,children:(0,k.jsx)(u,{role:"dialog",id:i.contentId,"aria-describedby":i.descriptionId,"aria-labelledby":i.titleId,"data-state":a3(i.open),...h,ref:m,onDismiss:()=>i.onOpenChange(!1)})}),(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)(a7,{titleId:i.titleId}),(0,k.jsx)(a8,{contentRef:l,descriptionId:i.descriptionId})]})]})}),a_="DialogTitle";g.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=aK(a_,c);return(0,k.jsx)(q.sG.h2,{id:e.titleId,...d,ref:b})}).displayName=a_;var a0="DialogDescription";g.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=aK(a0,c);return(0,k.jsx)(q.sG.p,{id:e.descriptionId,...d,ref:b})}).displayName=a0;var a1="DialogClose",a2=g.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=aK(a1,c);return(0,k.jsx)(q.sG.button,{type:"button",...d,ref:b,onClick:i(a.onClick,()=>e.onOpenChange(!1))})});function a3(a){return a?"open":"closed"}a2.displayName=a1;var a4="DialogTitleWarning",[a5,a6]=function(a,b){let c=g.createContext(b),d=a=>{let{children:b,...d}=a,e=g.useMemo(()=>d,Object.values(d));return(0,k.jsx)(c.Provider,{value:e,children:b})};return d.displayName=a+"Provider",[d,function(d){let e=g.useContext(c);if(e)return e;if(void 0!==b)return b;throw Error(`\`${d}\` must be used within \`${a}\``)}]}(a4,{contentName:aW,titleName:a_,docsSlug:"dialog"}),a7=({titleId:a})=>{let b=a6(a4),c=`\`${b.contentName}\` requires a \`${b.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${b.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${b.docsSlug}`;return g.useEffect(()=>{a&&(document.getElementById(a)||console.error(c))},[c,a]),null},a8=({contentRef:a,descriptionId:b})=>{let c=a6("DialogDescriptionWarning"),d=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${c.contentName}}.`;return g.useEffect(()=>{let c=a.current?.getAttribute("aria-describedby");b&&c&&(document.getElementById(b)||console.warn(d))},[d,a,b]),null},a9=aL,ba=aN,bb=aR,bc=aT,bd=aX,be=a2}};