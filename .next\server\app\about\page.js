(()=>{var a={};a.id=220,a.ids=[220],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11109:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,28770)),"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\about\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\app\\about\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/about/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},28770:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>m,metadata:()=>l});var d=c(37413),e=c(64544),f=c(64947),g=c(55952),h=c(40431),i=c(48616),j=c(85801),k=c(37094);let l={title:"About Us - Himalayan Brew Caf\xe9 | Our Story & Values in Biratnagar",description:"Discover the story behind Himalayan Brew Caf\xe9 in Biratnagar. Learn about our passion for coffee, commitment to community, and authentic Nepali hospitality since 2020.",keywords:"about Himalayan Brew, caf\xe9 story Biratnagar, Nepal coffee culture, local caf\xe9 Biratnagar, coffee shop history"};function m(){return(0,d.jsxs)("div",{className:"min-h-screen",children:[(0,d.jsx)(e.Navigation,{}),(0,d.jsxs)("main",{children:[(0,d.jsx)(g.AboutHero,{}),(0,d.jsx)(h.StorySection,{}),(0,d.jsx)(i.ValuesSection,{}),(0,d.jsx)(k.MissionSection,{}),(0,d.jsx)(j.TeamSection,{})]}),(0,d.jsx)(f.Footer,{})]})}},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33317:(a,b,c)=>{"use strict";c.d(b,{StorySection:()=>k});var d=c(60687),e=c(51743),f=c(44493),g=c(40228),h=c(13166),i=c(97992),j=c(40846);function k(){let a=j.I.story.split("\n\n");return(0,d.jsx)("section",{className:"py-20 bg-white",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6",children:"The Beginning of Our Journey"}),(0,d.jsx)("p",{className:"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed",children:"Every great story has a beginning. Ours started with a simple dream and a deep love for bringing people together over exceptional coffee."})]}),(0,d.jsxs)("div",{className:"grid lg:grid-cols-2 gap-16 items-center mb-20",children:[(0,d.jsx)(e.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.6},viewport:{once:!0},className:"space-y-6",children:a.map((a,b)=>(0,d.jsx)(e.P.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},viewport:{once:!0},className:"text-cafe-brown leading-relaxed text-lg",children:a},b))}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"aspect-[4/3] rounded-lg overflow-hidden shadow-cafe",children:(0,d.jsx)("img",{src:"https://images.unsplash.com/photo-1442975631115-c4f7b05b8a2c?w=400&h=300&fit=crop",alt:"Coffee preparation",className:"w-full h-full object-cover"})}),(0,d.jsx)("div",{className:"aspect-square rounded-lg overflow-hidden shadow-cafe",children:(0,d.jsx)("img",{src:"https://images.unsplash.com/photo-1559925393-8be0ec4767c8?w=300&h=300&fit=crop",alt:"Caf\xe9 interior",className:"w-full h-full object-cover"})})]}),(0,d.jsxs)("div",{className:"space-y-4 pt-8",children:[(0,d.jsx)("div",{className:"aspect-square rounded-lg overflow-hidden shadow-cafe",children:(0,d.jsx)("img",{src:"https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=300&fit=crop",alt:"Friends at caf\xe9",className:"w-full h-full object-cover"})}),(0,d.jsx)("div",{className:"aspect-[4/3] rounded-lg overflow-hidden shadow-cafe",children:(0,d.jsx)("img",{src:"https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=400&h=300&fit=crop",alt:"Coffee beans",className:"w-full h-full object-cover"})})]})]})]}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"grid md:grid-cols-3 gap-8",children:[(0,d.jsx)(f.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300",children:(0,d.jsxs)(f.Wu,{className:"p-6 text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-beige rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(g.A,{className:"h-8 w-8 text-cafe-brown"})}),(0,d.jsx)("h3",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-2",children:"2020 - The Dream"}),(0,d.jsx)("p",{className:"text-cafe-brown leading-relaxed",children:"Founded with a vision to create a community space that celebrates Nepal's rich coffee culture and brings people together."})]})}),(0,d.jsx)(f.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300",children:(0,d.jsxs)(f.Wu,{className:"p-6 text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-beige rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(h.A,{className:"h-8 w-8 text-cafe-brown"})}),(0,d.jsx)("h3",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-2",children:"2021 - Growth"}),(0,d.jsx)("p",{className:"text-cafe-brown leading-relaxed",children:"Established partnerships with local farmers in Ilam and Gulmi, ensuring the finest quality beans and supporting our community."})]})}),(0,d.jsx)(f.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300",children:(0,d.jsxs)(f.Wu,{className:"p-6 text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-beige rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(i.A,{className:"h-8 w-8 text-cafe-brown"})}),(0,d.jsx)("h3",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-2",children:"Today - Community Hub"}),(0,d.jsx)("p",{className:"text-cafe-brown leading-relaxed",children:"Proud to be Biratnagar's beloved gathering spot, serving hundreds of customers and creating countless memories daily."})]})})]})]})})}},33873:a=>{"use strict";a.exports=require("path")},37094:(a,b,c)=>{"use strict";c.d(b,{MissionSection:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call MissionSection() from the server but MissionSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about\\mission-section.tsx","MissionSection")},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},40431:(a,b,c)=>{"use strict";c.d(b,{StorySection:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call StorySection() from the server but StorySection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about\\story-section.tsx","StorySection")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41312:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},44493:(a,b,c)=>{"use strict";c.d(b,{Wu:()=>i,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},48616:(a,b,c)=>{"use strict";c.d(b,{ValuesSection:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ValuesSection() from the server but ValuesSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about\\values-section.tsx","ValuesSection")},48950:(a,b,c)=>{"use strict";c.d(b,{AboutHero:()=>j});var d=c(60687),e=c(51743),f=c(13166),g=c(41312),h=c(67760),i=c(54082);function j(){return(0,d.jsxs)("section",{className:"relative min-h-[70vh] flex items-center justify-center bg-cafe-pattern pt-16",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-cover bg-center bg-no-repeat",style:{backgroundImage:"url('https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?w=1920&h=1080&fit=crop')",backgroundBlendMode:"overlay"},children:(0,d.jsx)("div",{className:"absolute inset-0 bg-cafe-dark-brown/60"})}),(0,d.jsx)("div",{className:"relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white",children:(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"space-y-8",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(e.P.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.6,delay:.2},className:"flex justify-center",children:(0,d.jsx)(f.A,{className:"h-16 w-16 text-cafe-gold"})}),(0,d.jsx)("h1",{className:"font-serif text-5xl md:text-6xl font-bold",children:"Our Story"}),(0,d.jsx)("p",{className:"text-xl md:text-2xl text-cafe-cream font-medium max-w-3xl mx-auto",children:"Where Mountains Meet Coffee - A Journey of Passion, Community, and Authentic Nepali Hospitality"})]}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.6},className:"grid grid-cols-2 md:grid-cols-4 gap-8 mt-16",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(f.A,{className:"h-8 w-8 text-cafe-gold"})}),(0,d.jsx)("div",{className:"text-3xl font-bold text-cafe-gold mb-2",children:"2020"}),(0,d.jsx)("div",{className:"text-cafe-cream",children:"Founded"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(g.A,{className:"h-8 w-8 text-cafe-gold"})}),(0,d.jsx)("div",{className:"text-3xl font-bold text-cafe-gold mb-2",children:"500+"}),(0,d.jsx)("div",{className:"text-cafe-cream",children:"Happy Customers"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(h.A,{className:"h-8 w-8 text-cafe-gold"})}),(0,d.jsx)("div",{className:"text-3xl font-bold text-cafe-gold mb-2",children:"4.9"}),(0,d.jsx)("div",{className:"text-cafe-cream",children:"Rating"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(i.A,{className:"h-8 w-8 text-cafe-gold"})}),(0,d.jsx)("div",{className:"text-3xl font-bold text-cafe-gold mb-2",children:"100%"}),(0,d.jsx)("div",{className:"text-cafe-cream",children:"Local Sourced"})]})]})]})})]})}},54082:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},55952:(a,b,c)=>{"use strict";c.d(b,{AboutHero:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call AboutHero() from the server but AboutHero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about\\about-hero.tsx","AboutHero")},59975:(a,b,c)=>{"use strict";c.d(b,{TeamSection:()=>l});var d=c(60687),e=c(51743),f=c(44493),g=c(13166),h=c(41312),i=c(67760),j=c(86561);let k=[{id:"tm1",name:"Ramesh Sharma",role:"Founder & Head Barista",image:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face",bio:"With over 10 years of experience in the coffee industry, Ramesh founded Himalayan Brew with a vision to bring authentic Nepali coffee culture to Biratnagar.",icon:g.A},{id:"tm2",name:"Sita Gurung",role:"Operations Manager",image:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face",bio:"Sita ensures smooth daily operations and maintains our high standards of service. Her warm personality embodies our commitment to hospitality.",icon:h.A},{id:"tm3",name:"Arjun Rai",role:"Head Chef",image:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face",bio:"Arjun brings traditional Nepali flavors to our menu, specializing in authentic momos and dal bhat that remind customers of home.",icon:i.A},{id:"tm4",name:"Maya Thapa",role:"Community Relations",image:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300&h=300&fit=crop&crop=face",bio:"Maya builds bridges between our caf\xe9 and the Biratnagar community, organizing events and supporting local initiatives.",icon:j.A}];function l(){return(0,d.jsx)("section",{className:"py-20 bg-cafe-beige",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6",children:"Meet Our Team"}),(0,d.jsx)("p",{className:"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed",children:"Behind every great cup of coffee is a passionate team dedicated to creating exceptional experiences for our community."})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16",children:k.map((a,b)=>{let c=a.icon;return(0,d.jsx)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},viewport:{once:!0},children:(0,d.jsx)(f.Zp,{className:"h-full border-cafe-sand hover:shadow-cafe-lg transition-all duration-300 group bg-white",children:(0,d.jsxs)(f.Wu,{className:"p-6 text-center",children:[(0,d.jsxs)("div",{className:"relative mb-6",children:[(0,d.jsx)("div",{className:"w-24 h-24 mx-auto rounded-full overflow-hidden shadow-cafe",children:(0,d.jsx)("img",{src:a.image,alt:a.name,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"})}),(0,d.jsx)("div",{className:"absolute -bottom-2 -right-2 w-8 h-8 bg-cafe-gold rounded-full flex items-center justify-center shadow-cafe",children:(0,d.jsx)(c,{className:"h-4 w-4 text-cafe-dark-brown"})})]}),(0,d.jsx)("h3",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-2",children:a.name}),(0,d.jsx)("p",{className:"text-cafe-brown font-medium mb-4",children:a.role}),(0,d.jsx)("p",{className:"text-cafe-brown text-sm leading-relaxed",children:a.bio})]})})},a.id)})}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"text-center",children:(0,d.jsx)(f.Zp,{className:"border-cafe-sand bg-white shadow-cafe-lg max-w-2xl mx-auto",children:(0,d.jsxs)(f.Wu,{className:"p-8",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-brown/10 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,d.jsx)(h.A,{className:"h-8 w-8 text-cafe-brown"})}),(0,d.jsx)("h3",{className:"font-serif text-2xl font-semibold text-cafe-dark-brown mb-4",children:"Join Our Family"}),(0,d.jsx)("p",{className:"text-cafe-brown leading-relaxed mb-6",children:"We're always looking for passionate individuals who share our love for coffee and community. If you'd like to be part of the Himalayan Brew family, we'd love to hear from you."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)("a",{href:"mailto:<EMAIL>",className:"inline-flex items-center justify-center px-6 py-3 bg-cafe-brown text-cafe-cream rounded-lg font-semibold hover:bg-cafe-dark-brown transition-colors duration-200",children:"Send Your Resume"}),(0,d.jsx)("a",{href:"/contact",className:"inline-flex items-center justify-center px-6 py-3 border border-cafe-brown text-cafe-brown rounded-lg font-semibold hover:bg-cafe-brown hover:text-cafe-cream transition-colors duration-200",children:"Contact Us"})]})]})})})]})})}},62806:(a,b,c)=>{"use strict";c.d(b,{ValuesSection:()=>m});var d=c(60687),e=c(51743),f=c(44493),g=c(41312),h=c(86561),i=c(54082),j=c(67760),k=c(40846);let l={"Community First":g.A,"Quality & Authenticity":h.A,Sustainability:i.A,"Warm Hospitality":j.A};function m(){return(0,d.jsx)("section",{className:"py-20 bg-cafe-cream",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6",children:"Our Core Values"}),(0,d.jsx)("p",{className:"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed",children:"These fundamental principles guide every decision we make and every cup we serve, ensuring we stay true to our mission of bringing people together."})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 gap-8 mb-16",children:k.I.values.map((a,b)=>{let c=l[a.title];return(0,d.jsx)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},viewport:{once:!0},children:(0,d.jsx)(f.Zp,{className:"h-full border-cafe-beige hover:shadow-cafe-lg transition-all duration-300 group",children:(0,d.jsx)(f.Wu,{className:"p-8",children:(0,d.jsxs)("div",{className:"flex items-start gap-6",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/20 rounded-full flex items-center justify-center flex-shrink-0 group-hover:bg-cafe-gold/30 transition-colors duration-300",children:(0,d.jsx)(c,{className:"h-8 w-8 text-cafe-brown"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"font-serif text-2xl font-semibold text-cafe-dark-brown mb-4",children:a.title}),(0,d.jsx)("p",{className:"text-cafe-brown leading-relaxed text-lg",children:a.description})]})]})})})},a.title)})}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"text-center",children:(0,d.jsx)(f.Zp,{className:"border-cafe-beige bg-white shadow-cafe-lg max-w-4xl mx-auto",children:(0,d.jsxs)(f.Wu,{className:"p-12",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold rounded-full flex items-center justify-center mx-auto mb-6",children:(0,d.jsx)(j.A,{className:"h-8 w-8 text-cafe-dark-brown"})}),(0,d.jsx)("blockquote",{className:"text-2xl md:text-3xl font-serif text-cafe-dark-brown leading-relaxed mb-6 italic",children:"“Our caf\xe9 is more than just a place to grab coffee – it's a community hub where students study, friends catch up, and families create memories.”"}),(0,d.jsxs)("div",{className:"text-cafe-brown",children:[(0,d.jsx)("p",{className:"font-semibold",children:"Founder's Vision"}),(0,d.jsx)("p",{className:"text-sm",children:"Himalayan Brew Caf\xe9"})]})]})})})]})})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65315:(a,b,c)=>{Promise.resolve().then(c.bind(c,48950)),Promise.resolve().then(c.bind(c,67218)),Promise.resolve().then(c.bind(c,33317)),Promise.resolve().then(c.bind(c,59975)),Promise.resolve().then(c.bind(c,62806)),Promise.resolve().then(c.bind(c,94101)),Promise.resolve().then(c.bind(c,14246))},67218:(a,b,c)=>{"use strict";c.d(b,{MissionSection:()=>n});var d=c(60687),e=c(51743),f=c(44493),g=c(62688);let h=(0,g.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),i=(0,g.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),j=(0,g.A)("mountain",[["path",{d:"m8 3 4 8 5-5 5 15H2L8 3z",key:"otkl63"}]]);var k=c(54082),l=c(41312);let m=(0,g.A)("compass",[["path",{d:"m16.24 7.76-1.804 5.411a2 2 0 0 1-1.265 1.265L7.76 16.24l1.804-5.411a2 2 0 0 1 1.265-1.265z",key:"9ktpf1"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);function n(){return(0,d.jsx)("section",{className:"py-20 bg-white",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6",children:"Our Mission & Vision"}),(0,d.jsx)("p",{className:"text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed",children:"Driven by purpose and guided by vision, we're committed to making a positive impact in Biratnagar and beyond."})]}),(0,d.jsxs)("div",{className:"grid lg:grid-cols-2 gap-16 items-center mb-20",children:[(0,d.jsxs)(e.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.6},viewport:{once:!0},className:"space-y-8",children:[(0,d.jsx)(f.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300",children:(0,d.jsx)(f.Wu,{className:"p-8",children:(0,d.jsxs)("div",{className:"flex items-start gap-6",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-brown/10 rounded-full flex items-center justify-center flex-shrink-0",children:(0,d.jsx)(h,{className:"h-8 w-8 text-cafe-brown"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-serif text-2xl font-semibold text-cafe-dark-brown mb-4",children:"Our Mission"}),(0,d.jsx)("p",{className:"text-cafe-brown leading-relaxed text-lg",children:"To create a warm, welcoming space where the community of Biratnagar can come together over exceptional coffee, authentic Nepali cuisine, and meaningful conversations. We strive to support local farmers, preserve traditional flavors, and foster connections that enrich our community."})]})]})})}),(0,d.jsx)(f.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300",children:(0,d.jsx)(f.Wu,{className:"p-8",children:(0,d.jsxs)("div",{className:"flex items-start gap-6",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/10 rounded-full flex items-center justify-center flex-shrink-0",children:(0,d.jsx)(i,{className:"h-8 w-8 text-cafe-brown"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-serif text-2xl font-semibold text-cafe-dark-brown mb-4",children:"Our Vision"}),(0,d.jsx)("p",{className:"text-cafe-brown leading-relaxed text-lg",children:"To become the heart of Biratnagar's social and cultural life, known throughout Nepal as a model of sustainable business practices, community engagement, and authentic hospitality. We envision a future where every cup we serve contributes to a stronger, more connected community."})]})]})})})]}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"relative",children:[(0,d.jsx)("div",{className:"aspect-[4/3] rounded-2xl overflow-hidden shadow-cafe-lg",children:(0,d.jsx)("img",{src:"https://images.unsplash.com/photo-1521017432531-fbd92d768814?w=600&h=450&fit=crop",alt:"Caf\xe9 community gathering",className:"w-full h-full object-cover"})}),(0,d.jsx)("div",{className:"absolute -bottom-6 -left-6 w-24 h-24 bg-cafe-gold rounded-full flex items-center justify-center shadow-cafe-lg",children:(0,d.jsx)(j,{className:"h-12 w-12 text-cafe-dark-brown"})})]})]}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},children:[(0,d.jsx)("h3",{className:"font-serif text-3xl font-semibold text-cafe-dark-brown text-center mb-12",children:"Our Commitment to Biratnagar"}),(0,d.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,d.jsx)(f.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300 text-center",children:(0,d.jsxs)(f.Wu,{className:"p-6",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-green/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(k.A,{className:"h-8 w-8 text-cafe-green"})}),(0,d.jsx)("h4",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-3",children:"Environmental Responsibility"}),(0,d.jsx)("p",{className:"text-cafe-brown leading-relaxed",children:"Supporting sustainable farming practices and reducing our environmental footprint through eco-friendly operations."})]})}),(0,d.jsx)(f.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300 text-center",children:(0,d.jsxs)(f.Wu,{className:"p-6",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-brown/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(l.A,{className:"h-8 w-8 text-cafe-brown"})}),(0,d.jsx)("h4",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-3",children:"Community Support"}),(0,d.jsx)("p",{className:"text-cafe-brown leading-relaxed",children:"Actively participating in local events, supporting local artists, and creating opportunities for community engagement."})]})}),(0,d.jsx)(f.Zp,{className:"border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300 text-center",children:(0,d.jsxs)(f.Wu,{className:"p-6",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-cafe-gold/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(m,{className:"h-8 w-8 text-cafe-brown"})}),(0,d.jsx)("h4",{className:"font-serif text-xl font-semibold text-cafe-dark-brown mb-3",children:"Cultural Preservation"}),(0,d.jsx)("p",{className:"text-cafe-brown leading-relaxed",children:"Celebrating and preserving Nepal's rich coffee culture while embracing modern caf\xe9 experiences."})]})})]})]})]})})}},67760:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},85801:(a,b,c)=>{"use strict";c.d(b,{TeamSection:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call TeamSection() from the server but TeamSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\cafe\\src\\components\\about\\team-section.tsx","TeamSection")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86561:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},89291:(a,b,c)=>{Promise.resolve().then(c.bind(c,55952)),Promise.resolve().then(c.bind(c,37094)),Promise.resolve().then(c.bind(c,40431)),Promise.resolve().then(c.bind(c,85801)),Promise.resolve().then(c.bind(c,48616)),Promise.resolve().then(c.bind(c,64947)),Promise.resolve().then(c.bind(c,64544))}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,453,953,180],()=>b(b.s=11109));module.exports=c})();