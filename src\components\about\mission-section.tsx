"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Target, Eye, Compass, Mountain, Leaf, Users } from "lucide-react";
import Image from "next/image";

export function MissionSection() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="font-serif text-4xl md:text-5xl font-bold text-cafe-dark-brown mb-6">
            Our Mission & Vision
          </h2>
          <p className="text-xl text-cafe-brown max-w-3xl mx-auto leading-relaxed">
            Driven by purpose and guided by vision, we&apos;re committed to making a positive impact in Biratnagar and beyond.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Mission & Vision Cards */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {/* Mission Card */}
            <Card className="border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300">
              <CardContent className="p-8">
                <div className="flex items-start gap-6">
                  <div className="w-16 h-16 bg-cafe-brown/10 rounded-full flex items-center justify-center flex-shrink-0">
                    <Target className="h-8 w-8 text-cafe-brown" />
                  </div>
                  <div>
                    <h3 className="font-serif text-2xl font-semibold text-cafe-dark-brown mb-4">
                      Our Mission
                    </h3>
                    <p className="text-cafe-brown leading-relaxed text-lg">
                      To create a warm, welcoming space where the community of Biratnagar can come together over exceptional coffee, authentic Nepali cuisine, and meaningful conversations. We strive to support local farmers, preserve traditional flavors, and foster connections that enrich our community.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Vision Card */}
            <Card className="border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300">
              <CardContent className="p-8">
                <div className="flex items-start gap-6">
                  <div className="w-16 h-16 bg-cafe-gold/10 rounded-full flex items-center justify-center flex-shrink-0">
                    <Eye className="h-8 w-8 text-cafe-brown" />
                  </div>
                  <div>
                    <h3 className="font-serif text-2xl font-semibold text-cafe-dark-brown mb-4">
                      Our Vision
                    </h3>
                    <p className="text-cafe-brown leading-relaxed text-lg">
                      To become the heart of Biratnagar&apos;s social and cultural life, known throughout Nepal as a model of sustainable business practices, community engagement, and authentic hospitality. We envision a future where every cup we serve contributes to a stronger, more connected community.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Image */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="aspect-[4/3] rounded-2xl overflow-hidden shadow-cafe-lg">
              <Image
                src="https://images.unsplash.com/photo-1521017432531-fbd92d768814?w=600&h=450&fit=crop"
                alt="Café community gathering"
                width={600}
                height={450}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="absolute -bottom-6 -left-6 w-24 h-24 bg-cafe-gold rounded-full flex items-center justify-center shadow-cafe-lg">
              <Mountain className="h-12 w-12 text-cafe-dark-brown" />
            </div>
          </motion.div>
        </div>

        {/* Goals Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <h3 className="font-serif text-3xl font-semibold text-cafe-dark-brown text-center mb-12">
            Our Commitment to Biratnagar
          </h3>
          
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300 text-center">
              <CardContent className="p-6">
                <div className="w-16 h-16 bg-cafe-green/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Leaf className="h-8 w-8 text-cafe-green" />
                </div>
                <h4 className="font-serif text-xl font-semibold text-cafe-dark-brown mb-3">
                  Environmental Responsibility
                </h4>
                <p className="text-cafe-brown leading-relaxed">
                  Supporting sustainable farming practices and reducing our environmental footprint through eco-friendly operations.
                </p>
              </CardContent>
            </Card>

            <Card className="border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300 text-center">
              <CardContent className="p-6">
                <div className="w-16 h-16 bg-cafe-brown/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8 text-cafe-brown" />
                </div>
                <h4 className="font-serif text-xl font-semibold text-cafe-dark-brown mb-3">
                  Community Support
                </h4>
                <p className="text-cafe-brown leading-relaxed">
                  Actively participating in local events, supporting local artists, and creating opportunities for community engagement.
                </p>
              </CardContent>
            </Card>

            <Card className="border-cafe-beige hover:shadow-cafe-lg transition-shadow duration-300 text-center">
              <CardContent className="p-6">
                <div className="w-16 h-16 bg-cafe-gold/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Compass className="h-8 w-8 text-cafe-brown" />
                </div>
                <h4 className="font-serif text-xl font-semibold text-cafe-dark-brown mb-3">
                  Cultural Preservation
                </h4>
                <p className="text-cafe-brown leading-relaxed">
                  Celebrating and preserving Nepal&apos;s rich coffee culture while embracing modern café experiences.
                </p>
              </CardContent>
            </Card>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
